MultiFeed Syncer (SQL): 
            SELECT cd.`name`, c.`category_id`
            FROM `oc_category` c
            LEFT JOIN `oc_category_description` cd ON (c.`category_id` = cd.`category_id`)
            WHERE cd.`language_id` = '1'
            AND cd.`name` LIKE '%употребявана%'
            AND c.`status` = '1'
            ORDER BY cd.`name` ASC
            LIMIT 10
MultiFeed Syncer (SQL): SELECT * FROM `oc_multi_feed_syncer_connectors` ORDER BY `connector` ASC
MultiFeed Syncer (SQL): SELECT COUNT(*) AS total FROM `oc_multi_feed_syncer_logs`
MultiFeed Syncer (SQL): SELECT mfs.*, mfc.connector FROM `oc_multi_feed_syncer_logs` mfs LEFT JOIN `oc_multi_feed_syncer_connectors` mfc ON (mfs.mfsc_id = mfc.mfsc_id) ORDER BY mfs.process_date DESC LIMIT 0,10
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
MultiFeed Syncer (SQL): 
                SELECT
                    `status`,
                    COUNT(*) as count,
                    AVG(`attempts`) as avg_attempts
                FROM `oc_product_image_download_queue`
                GROUP BY `status`
            
MultiFeed Syncer (SQL): SELECT * FROM oc_multi_feed_syncer_connectors WHERE mfsc_id = '1'
