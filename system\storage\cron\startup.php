<?php

$https_host = 'noxes.org';

// Store
$query = 
    $registry->get('db')->query("SELECT * FROM " . DB_PREFIX . "store WHERE REPLACE(`ssl`, 'www.', '') = '" . 
    $registry->get('db')->escape('https://' . str_replace('www.', '', $https_host) . rtrim(dirname($_SERVER['PHP_SELF']), '/.\\') . '/') . "'");

if (isset($registry->get('request')->get['store_id'])) {
    $registry->get('config')->set('config_store_id', (int)$registry->get('request')->get['store_id']);
} else if ($query->num_rows) {
    $registry->get('config')->set('config_store_id', $query->row['store_id']);
} else {
    $registry->get('config')->set('config_store_id', 0);
}

if (!$query->num_rows) {
    $registry->get('config')->set('config_url', 'https://' . $https_host . '/');
    $registry->get('config')->set('config_ssl', 'https://' . $https_host . '/');
}

// Settings
$query = $registry->get('db')->query("SELECT * FROM `" . DB_PREFIX . "setting` WHERE store_id = '0' OR store_id = '" . (int)$registry->get('config')->get('config_store_id') . "' ORDER BY store_id ASC");

foreach ($query->rows as $result) {
    if (!$result['serialized']) {
        $registry->get('config')->set($result['key'], $result['value']);
    } else {
        $registry->get('config')->set($result['key'], json_decode($result['value'], true));
    }
}

// Url
$registry->set('url', new Url($registry->get('config')->get('config_url'), $registry->get('config')->get('config_ssl')));

// Language
$code = '';

// Директно вземане на езиците от базата данни вместо използване на модела
$query = $registry->get('db')->query("SELECT * FROM " . DB_PREFIX . "language WHERE status = '1'");
$languages = [];
foreach ($query->rows as $result) {
    $languages[$result['code']] = [
        'language_id' => $result['language_id'],
        'name' => $result['name'],
        'code' => $result['code'],
        'locale' => $result['locale'],
        'image' => $result['image'],
        'directory' => $result['directory'],
        'sort_order' => $result['sort_order'],
        'status' => $result['status']
    ];
}

if (isset($registry->get('session')->data['language'])) {
    $code = $registry->get('session')->data['language'];
}
        
if (isset($registry->get('request')->cookie['language']) && !array_key_exists($code, $languages)) {
    $code = $registry->get('request')->cookie['language'];
}

// Language Detection
if (!empty($registry->get('request')->server['HTTP_ACCEPT_LANGUAGE']) && !array_key_exists($code, $languages)) {
    $detect = '';
    
    $browser_languages = explode(',', $registry->get('request')->server['HTTP_ACCEPT_LANGUAGE']);
    
    // Try using local to detect the language
    foreach ($browser_languages as $browser_language) {
        foreach ($languages as $key => $value) {
            if ($value['status']) {
                $locale = explode(',', $value['locale']);
                
                if (in_array($browser_language, $locale)) {
                    $detect = $key;
                    break 2;
                }
            }
        }	
    }			
    
    if (!$detect) { 
        // Try using language folder to detect the language
        foreach ($browser_languages as $browser_language) {
            if (array_key_exists(strtolower($browser_language), $languages)) {
                $detect = strtolower($browser_language);
                
                break;
            }
        }
    }
    
    $code = $detect ? $detect : '';
}

if (!array_key_exists($code, $languages)) {
    $code = $registry->get('config')->get('config_language');
}

if (!isset($registry->get('session')->data['language']) || $registry->get('session')->data['language'] != $code) {
    $registry->get('session')->data['language'] = $code;
}
        
if (!isset($registry->get('request')->cookie['language']) || $registry->get('request')->cookie['language'] != $code) {
    // В крон задачите няма HTTP_HOST, затова използваме директно домейна
    setcookie('language', $code, time() + 60 * 60 * 24 * 30, '/', isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'dundio.com');
}
        
// Overwrite the default language object
$language = new Language($code);
$language->load($code);

$registry->set('language', $language);

// Set the config language_id
$registry->get('config')->set('config_language_id', $languages[$code]['language_id']);	

// Customer
$customer = new Cart\Customer($registry);
$registry->set('customer', $customer);

// Customer Group
if ($registry->get('customer')->isLogged()) {
    $registry->get('config')->set('config_customer_group_id', $registry->get('customer')->getGroupId());
} elseif (isset($registry->get('session')->data['customer']) && isset($registry->get('session')->data['customer']['customer_group_id'])) {
    // For API calls
    $registry->get('config')->set('config_customer_group_id', $registry->get('session')->data['customer']['customer_group_id']);
} elseif (isset($registry->get('session')->data['guest']) && isset($registry->get('session')->data['guest']['customer_group_id'])) {
    $registry->get('config')->set('config_customer_group_id', $registry->get('session')->data['guest']['customer_group_id']);
}

// Tracking Code
if (isset($registry->get('request')->get['tracking'])) {
    setcookie('tracking', $registry->get('request')->get['tracking'], time() + 3600 * 24 * 1000, '/');

    $registry->get('db')->query("UPDATE `" . DB_PREFIX . "marketing` SET clicks = (clicks + 1) WHERE code = '" . $registry->get('db')->escape($registry->get('request')->get['tracking']) . "'");
}		

// Affiliate
// $registry->set('affiliate', new Cart\Affiliate($registry));

// Currency
$code = '';

// Директно вземане на валутите от базата данни вместо използване на модела
$query = $registry->get('db')->query("SELECT * FROM " . DB_PREFIX . "currency WHERE status = '1'");

$currencies = [];
foreach ($query->rows as $result) {
    $currencies[$result['code']] = [
        'currency_id' => $result['currency_id'],
        'title' => $result['title'],
        'code' => $result['code'],
        'symbol_left' => $result['symbol_left'],
        'symbol_right' => $result['symbol_right'],
        'decimal_place' => $result['decimal_place'],
        'value' => $result['value'],
        'status' => $result['status'],
        'date_modified' => $result['date_modified']
    ];
}

if (isset($registry->get('session')->data['currency'])) {
    $code = $registry->get('session')->data['currency'];
}

if (isset($registry->get('request')->cookie['currency']) && !array_key_exists($code, $currencies)) {
    $code = $registry->get('request')->cookie['currency'];
}

if (!array_key_exists($code, $currencies)) {
    $code = $registry->get('config')->get('config_currency');
}

if (!isset($registry->get('session')->data['currency']) || $registry->get('session')->data['currency'] != $code) {
    $registry->get('session')->data['currency'] = $code;
}

if (!isset($registry->get('request')->cookie['currency']) || $registry->get('request')->cookie['currency'] != $code) {
    // В крон задачите няма HTTP_HOST, затова използваме директно домейна
    setcookie('currency', $code, time() + 60 * 60 * 24 * 30, '/', isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'dundio.com');
}		

$registry->set('currency', new Cart\Currency($registry));

// Tax
$registry->set('tax', new Cart\Tax($registry));

if (isset($registry->get('session')->data['shipping_address'])) {
    $registry->get('tax')->setShippingAddress($registry->get('session')->data['shipping_address']['country_id'], $registry->get('session')->data['shipping_address']['zone_id']);
} elseif ($registry->get('config')->get('config_tax_default') == 'shipping') {
    $registry->get('tax')->setShippingAddress($registry->get('config')->get('config_country_id'), $registry->get('config')->get('config_zone_id'));
}

if (isset($registry->get('session')->data['payment_address'])) {
    $registry->get('tax')->setPaymentAddress($registry->get('session')->data['payment_address']['country_id'], $registry->get('session')->data['payment_address']['zone_id']);
} elseif ($registry->get('config')->get('config_tax_default') == 'payment') {
    $registry->get('tax')->setPaymentAddress($registry->get('config')->get('config_country_id'), $registry->get('config')->get('config_zone_id'));
}

$registry->get('tax')->setStoreAddress($registry->get('config')->get('config_country_id'), $registry->get('config')->get('config_zone_id'));

// Weight
$registry->set('weight', new Cart\Weight($registry));

// Length
$registry->set('length', new Cart\Length($registry));

// Cart
// $registry->set('cart', new Cart\Cart($registry));

// Encryption
$registry->set('encryption', new Encryption($registry->get('config')->get('config_encryption')));


