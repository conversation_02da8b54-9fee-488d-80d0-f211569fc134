<?php
class ModelExtensionModuleMultiFeedSyncerConnectorsEoffice extends Model {
    private $connector_key = 'eoffice'; // Ключ на конектора (латиница) [cite: 6]
    private $connector_name = 'Eoffice.bg'; // Име на доставчика [cite: 6]
    // URL за извличане на XML данни от Eoffice.bg [cite: 27]
    private $xml_url = 'https://eoffice.bg/module.php?ModuleName=com.seliton.superxmlexport&Username=Noxes&Domain=noxes.org&Signature=4f4d3d55284c1036e8c061472d884cde62662952&DealerAccountType=0';

    /**
     * Връща данни, необходими за регистриране на този конектор 
     * в таблицата `multi_feed_syncer_connectors`. [cite: 15]
     * @return array
     */
    public function getInstallData() { 
        return array(
            'connector'     => $this->connector_name,
            'connector_key' => $this->connector_key,
            'markup_percentage' => 20.00
        );
    }

    /**
     * Връща типа на връзката (напр. "XML Link", "API"). [cite: 8]
     * @return string
     */
    public function getConnectionType() { 
        $text_copy_link = 'Копирай'; 
        $text_copied_link = 'Копирано!';
        $text_copy_error = 'Грешка!';

        $html = 'XML Линк: <span class="text-primary" data-toggle="tooltip" data-placement="top" title="' . htmlspecialchars($this->xml_url) . '">Виж линк</span>';
        $html .= ' <div class="btn-group">';
        $html .= '<button type="button" class="btn btn-default btn-xs btn-copy" data-clipboard-text="' . htmlspecialchars($this->xml_url) . '" title="Копирай XML линка">';
        $html .= '<i class="fa fa-copy"></i> <span class="copy-text">' . $text_copy_link . '</span>';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '<script>';
        $html .= '    $(document).ready(function() {' . "\n";
        $html .= '        $(".btn-copy").off("click.customCopy").on("click.customCopy", function(e) {' . "\n";
        $html .= '            e.preventDefault();' . "\n";
        $html .= '            var $button = $(this);' . "\n";
        $html .= '            var linkText = $button.data("clipboard-text");' . "\n";
        $html .= '            var $copyTextSpan = $button.find(".copy-text");' . "\n";
        $html .= '            var originalText = $copyTextSpan.text();' . "\n";
        $html .= '            var $icon = $button.find("i");' . "\n";
        $html .= '            var originalIconClass = $icon.attr("class");' . "\n";
        $html .= "\n";
        $html .= '            if (linkText && linkText.trim() !== "") {' . "\n";
        $html .= '                navigator.clipboard.writeText(linkText).then(function() {' . "\n";
        $html .= '                    $button.addClass("btn-success");' . "\n";
        $html .= '                    $copyTextSpan.text("' . $text_copied_link . '");' . "\n";
        $html .= '                    $icon.attr("class", "fa fa-check");' . "\n";
        $html .= "\n";
        $html .= '                    var $copiedMsg = $("<span>Копирано</span>").css({' . "\n";
        $html .= '                        "position": "fixed", "top": "20px", "left": "50%", "transform": "translateX(-50%)",' . "\n";
        $html .= '                        "background-color": "#4CAF50", "color": "white", "padding": "10px 20px",' . "\n";
        $html .= '                        "border-radius": "5px", "z-index": "10000", "font-size": "16px", "display": "none"' . "\n";
        $html .= '                    });' . "\n";
        $html .= '                    $("body").append($copiedMsg);' . "\n";
        $html .= '                    $copiedMsg.fadeIn(200).delay(1500).fadeOut(500, function() { $(this).remove(); });' . "\n";
        $html .= "\n";
        $html .= '                    setTimeout(function() {' . "\n";
        $html .= '                        $button.removeClass("btn-success");' . "\n";
        $html .= '                        $copyTextSpan.text(originalText);' . "\n";
        $html .= '                        $icon.attr("class", originalIconClass);' . "\n";
        $html .= '                    }, 2000);' . "\n";
        $html .= '                }).catch(function(err) {' . "\n";
        $html .= '                    console.error("Грешка при копиране: ", err);' . "\n";
        $html .= '                    $copyTextSpan.text("' . $text_copy_error . '");' . "\n";
        $html .= '                    $icon.attr("class", "fa fa-times");' . "\n";
        $html .= "\n";
        $html .= '                    var $errorMsg = $("<span>Грешка при копиране</span>").css({' . "\n";
        $html .= '                        "position": "fixed", "top": "20px", "left": "50%", "transform": "translateX(-50%)",';
        $html .= '                        "background-color": "#f44336", "color": "white", "padding": "10px 20px",';
        $html .= '                        "border-radius": "5px", "z-index": "10000", "font-size": "16px", "display": "none"';
        $html .= '                    });' . "\n";
        $html .= '                    $("body").append($errorMsg);' . "\n";
        $html .= '                    $errorMsg.fadeIn(200).delay(2000).fadeOut(500, function() { $(this).remove(); });' . "\n";
        $html .= "\n";
        $html .= '                    setTimeout(function() {' . "\n";
        $html .= '                        $copyTextSpan.text(originalText);' . "\n";
        $html .= '                        $icon.attr("class", originalIconClass);' . "\n";
        $html .= '                    }, 2500);' . "\n";
        $html .= '                });' . "\n";
        $html .= '            } else {' . "\n";
        $html .= '                console.warn("Няма текст за копиране в data-clipboard-text атрибута на бутона.");' . "\n";
        $html .= '            }' . "\n";
        $html .= '        });' . "\n";
        $html .= '    });' . "\n";
        $html .= '</script>';
        // Вашият CSS стил:
        $html .= '<style>.btn-copy { position: relative; transition: all 0.2s ease-in-out; } .btn-copy.btn-success { background-color: #5cb85c !important; border-color: #4cae4c !important; color: #fff !important; } .btn-copy.btn-success .fa-copy { display:none; } .btn-copy .fa-check { display:none; } .btn-copy.btn-success .fa-check { display:inline-block; }</style>';
        return $html;
    }

    /**
     * Връща информация за настройка на cron задача. [cite: 10]
     * @return string
     */
    public function getCronInfo() { 
        // Пътят до cron файла [cite: 25, 26]
        $cron_file_path = DIR_STORAGE . 'cron/multi_feed_syncer.php'; 

        // Командата може да изглежда така, ако искаме да предадем ключа на конектора:
        // $command = '/usr/bin/php ' . $cron_file_path . ' ' . $this->connector_key . ' >> ' . DIR_LOGS . $this->connector_key . '_sync.log 2>&1';
        // Или, ако главният скрипт обработва всички:
        $command = '/usr/local/bin/php ' . $cron_file_path . ' eoffice >> ' . DIR_LOGS . 'multi_feed_syncer.log 2>&1'; 
        return $command;
    }
    
    /**
     * Проверява дали източникът на данни (напр. XML линк) е достъпен.
     * @return bool True ако е достъпен, false в противен случай.
     */
    public function checkConnection() {
        $ch = curl_init($this->xml_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true); // Нужни са ни само хедърите за проверка на статуса
        curl_setopt($ch, CURLOPT_NOBODY, true); // Не ни е нужно тялото на отговора
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Таймаут 10 секунди
        curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return ($http_code >= 200 && $http_code < 300);
    }

    /**
     * Извлича данни от доставчика. [cite: 27]
     * @return string Сурови данни (напр. XML низ).
     */
    public function requestSyncData() { 
        $ch = curl_init($this->xml_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300); // Увеличен таймаут за потенциално големи файлове
        $xml_data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code == 200 && $xml_data) {
            return $xml_data;
        }
        return false; // Връща false при грешка
    }

    /**
     * Конвертира XML данни в PHP масив. [cite: 41]
     * @param string $xml_data
     * @return array
     */
    public function convertXMLdataToArray($xml_data) { 
        $xml = simplexml_load_string($xml_data, "SimpleXMLElement", LIBXML_NOCDATA);
        $json = json_encode($xml);
        $array = json_decode($json, TRUE);
        // Това е базова конверсия, може да се нуждае от подобрение 
        // в зависимост от специфичната обработка на XML структурата.
        return $array; 
    }

    /**
     * Мапира масива с данни от доставчика към структурата на Opencart продукт. [cite: 42]
     * @param array $supplier_product_data Данните за един продукт от доставчика
     * @param array $full_feed_data Пълният фийд данни (за достъп до глобални елементи като OptionGroups)
     * @return array Масив с данни за Opencart продукт.
     */
    public function mapDataToOpencartProduct(array $supplier_product_data, array $full_feed_data = []) {
        $opencart_product = $this->_initializeOpencartProduct();

        // --- СПЕЦИФИЧНО МАСИРАНЕ ЗА EOFFICE.BG ---
        // Това мапиране трябва да бъде внимателно имплементирано 
        // на базата на XML структурата на Eoffice. [cite: 38]
        $this->_mapBasicProductData($opencart_product, $supplier_product_data);
        $this->_mapProductImages($opencart_product, $supplier_product_data);
        $this->_mapProductWeight($opencart_product, $supplier_product_data);
        $this->_mapProductStockStatus($opencart_product, $supplier_product_data);
        $this->_mapProductCategories($opencart_product, $supplier_product_data);
        $this->_mapProductManufacturer($opencart_product, $supplier_product_data);
        $this->_mapProductAttributes($opencart_product, $supplier_product_data);
        $this->_mapProductSeoData($opencart_product, $supplier_product_data);
        $this->_mapProductOptions($opencart_product, $supplier_product_data, $full_feed_data);

        return $opencart_product;
    }

    /**
     * Извлича текстова стойност от потенциално вложен масив, върнат от _convertDomNodeToArray.
     *
     * @param mixed $node_data Данните от XML възела.
     * @param string $default Стойност по подразбиране, ако не се намери текстова стойност.
     * @return string Текстовата стойност или стойността по подразбиране.
     */
    private function _getXmlNodeValue($node_data, $default = '') {
        if (is_array($node_data)) {
            if (isset($node_data['#text'])) {
                return (string)$node_data['#text'];
            }
            if (isset($node_data[0]) && is_string($node_data[0])) {
                 return (string)$node_data[0];
            }
            // Ако е масив, но не можем да определим текстова стойност, връщаме default
            // trigger_error("Could not extract single string value from array: " . print_r($node_data, true), E_USER_NOTICE);
            return $default;
        }
        return (string)$node_data;
    }

    private function _initializeOpencartProduct() {
        $opencart_product = [];
        $oc_fields = [
            'product_id', 'name', 'description', 'meta_title', 'meta_description', 
            'meta_keyword', 'tag', 'model', 'sku', 'upc', 'ean', 'jan', 'isbn', 'mpn', 
            'location', 'quantity', 'stock_status_id', 'image', 'manufacturer_id', 
            'manufacturer', 'price', 'special', 'reward', 'points', 'tax_class_id', 
            'date_available', 'weight', 'weight_class_id', 'length', 'width', 'height', 
            'length_class_id', 'subtract', 'minimum', 'sort_order', 'status', 
            'date_added', 'date_modified', 'viewed', 
            'product_category', 'product_attribute', 'product_option', 'product_image',
            // Допълнителни полета за съхранение на данни от източника, които ще се обработват в doSync
            'categories_data_source', 'attributes_data_source', 'options_data_source', 'manufacturer_name_source'
        ];

        foreach ($oc_fields as $field) {
            $opencart_product[$field] = '';
            if (in_array($field, ['product_category', 'product_attribute', 'product_option', 'product_image', 'categories_data_source', 'attributes_data_source', 'options_data_source'])) {
                $opencart_product[$field] = [];
            }
        }
        return $opencart_product;
    }

    private function _mapBasicProductData(array &$opencart_product, array $supplier_product_data) {
        $opencart_product['model'] = isset($supplier_product_data['ProductCode']) ? $this->_getXmlNodeValue($supplier_product_data['ProductCode']) : '';
        $opencart_product['sku'] = isset($supplier_product_data['ProductCode']) ? $this->_getXmlNodeValue($supplier_product_data['ProductCode']) : ''; 
        $opencart_product['mpn'] = isset($supplier_product_data['ProductBarcode']) ? $this->_getXmlNodeValue($supplier_product_data['ProductBarcode']) : '';

        // За ProductName и ProductDescription, които имат езикови версии като ['BG' => 'текст']
        // _getXmlNodeValue може да не е подходящ директно, ако $supplier_product_data['ProductName'] е масивът с езиците.
        // Трябва да се запази логиката за достъп до конкретния език, а _getXmlNodeValue да се приложи върху самата стойност.
        if (isset($supplier_product_data['ProductName']['BG'])) {
            $product_name_bg = $this->_getXmlNodeValue($supplier_product_data['ProductName']['BG']);
            $opencart_product['name'] = $product_name_bg;
            if (empty($opencart_product['meta_title'])) {
                 $opencart_product['meta_title'] = $product_name_bg; 
            }
        }

        if (isset($supplier_product_data['ProductDescription']['BG'])) {
            $description_bg = $this->_getXmlNodeValue($supplier_product_data['ProductDescription']['BG']);
            if (!empty(trim(strip_tags($description_bg)))) {
                $opencart_product['description'] = $description_bg;
            }
        } else if (isset($supplier_product_data['ProductDetailedDescription']['BG'])) {
            // Ако ProductDescription липсва или е празен, проверяваме ProductDetailedDescription
            $detailed_description_bg = $this->_getXmlNodeValue($supplier_product_data['ProductDetailedDescription']['BG']);
            if (!empty(trim(strip_tags($detailed_description_bg)))) {
                $opencart_product['description'] = $detailed_description_bg;
            }
        }
        
        $opencart_product['quantity'] = isset($supplier_product_data['ProductQuantity']) ? (float)$this->_getXmlNodeValue($supplier_product_data['ProductQuantity'], 0) : 0;
        $opencart_product['price'] = isset($supplier_product_data['ProductPrice']) ? (float)$this->_getXmlNodeValue($supplier_product_data['ProductPrice'], 0.00) : 0.00;

        $product_is_active = isset($supplier_product_data['ProductIsActive']) ? $this->_getXmlNodeValue($supplier_product_data['ProductIsActive']) : 'no';
        $opencart_product['status'] = ($product_is_active == 'yes') ? 1 : 0;
        
        $created_timestamp_str = isset($supplier_product_data['ProductCreatedTimestamp']) ? $this->_getXmlNodeValue($supplier_product_data['ProductCreatedTimestamp']) : '';
        $opencart_product['date_available'] = !empty($created_timestamp_str) ? date('Y-m-d', strtotime($created_timestamp_str)) : date('Y-m-d');
    }

    private function _mapProductImages(array &$opencart_product, array $supplier_product_data) {
        if (isset($supplier_product_data['ProductImages']['ProductImage'])) {
            $images_node = $supplier_product_data['ProductImages']['ProductImage'];
            // $opencart_product['image'] е инициализирано с '' в _initializeOpencartProduct
            // $opencart_product['product_image'] е инициализирано с [] в _initializeOpencartProduct

            if (isset($images_node['ImagePath'])) { // Случай 1: Единично изображение (XML структурата показва, че това е възел, не масив)
                $opencart_product['image'] = $this->_getXmlNodeValue($images_node['ImagePath']);
            } elseif (is_array($images_node) && !empty($images_node)) { // Случай 2: Множество изображения (XML структурата показва, че ProductImage може да е масив)
                // Опитваме да зададем основното изображение от първия елемент на масива, ако има такъв и е валиден път.
                if (isset($images_node[0]['ImagePath'])) {
                    $main_image_path = $this->_getXmlNodeValue($images_node[0]['ImagePath']);
                    if (!empty($main_image_path)) {
                        $opencart_product['image'] = $main_image_path;
                    }
                }

                $sort_order = 0;
                foreach ($images_node as $img_data) {
                    if (isset($img_data['ImagePath'])) {
                        $current_image_path = $this->_getXmlNodeValue($img_data['ImagePath']);
                        if (!empty($current_image_path)) {
                            if (empty($opencart_product['image'])) {
                                // Ако основното изображение все още не е зададено (напр. $images_node[0]['ImagePath'] е липсвало или е било празно),
                                // това изображение става основно.
                                $opencart_product['image'] = $current_image_path;
                            }
                            $opencart_product['product_image'][] = [
                                'image'      => $current_image_path,
                                'sort_order' => $sort_order++
                            ];
                        }
                        // Ако $current_image_path е същият като $opencart_product['image'] (и основното е зададено), не правим нищо,
                        // за да избегнем дублирането на основното изображение в списъка с допълнителни.
                    }
                }
            }
        }
    }

    private function _mapProductWeight(array &$opencart_product, array $supplier_product_data) {
        $opencart_product['weight'] = isset($supplier_product_data['ProductWeight']) ? (float)$this->_getXmlNodeValue($supplier_product_data['ProductWeight'], 0.00) : 0.00;
        // Трябва да се мапират мерните единици за тегло към weight_class_id в Opencart. 
        // Засега това остава като коментар, тъй като изисква допълнителна логика или конфигурация
        // за определяне на weight_class_id на базата на мерната единица от фийда (ако е налична).
        // $opencart_product['weight_class_id'] = ...;
    }

    private function _mapProductStockStatus(array &$opencart_product, array $supplier_product_data) {
        // stock_status_id трябва да се настрои на база наличност и конфигурацията на магазина
        // $opencart_product['quantity'] вече трябва да е попълнено от _mapBasicProductData
        $opencart_product['stock_status_id'] = $opencart_product['quantity'] > 0 ? $this->config->get('config_stock_status_id') : $this->config->get('config_out_of_stock_status_id');
    }

    private function _mapProductCategories(array &$opencart_product, array $supplier_product_data) {
        // Нужна е логика за намиране или създаване на категории в Opencart и присвояване на продукта към тях.
        // XML предоставя CategoryID, CategoryName, CategoryBranch.
        // За улеснение, тук ще съхраним само данните; `doSync` ще трябва да ги обработи.
        // Opencart очаква масив от ID-та на категории в 'product_category'.
        // $opencart_product['categories_data_source'] е инициализиран в _initializeOpencartProduct
        if (isset($supplier_product_data['Category'])) {
            $main_category = $supplier_product_data['Category'];
            $main_cat_id = isset($main_category['CategoryID']) ? $this->_getXmlNodeValue($main_category['CategoryID']) : '';
            if(!empty($main_cat_id)) {
                $opencart_product['categories_data_source'][] = [
                    'id' => $main_cat_id,
                    'name' => isset($main_category['CategoryName']['BG']) ? $this->_getXmlNodeValue($main_category['CategoryName']['BG']) : '',
                    'branch' => isset($main_category['CategoryBranch']['BG']) ? $this->_getXmlNodeValue($main_category['CategoryBranch']['BG']) : ''
                ];
            }
        }
        if (isset($supplier_product_data['AdditionalCategories']['Category'])) {
            $additional_categories = $supplier_product_data['AdditionalCategories']['Category'];
            // Handle case where there's only one 'Category' which is not an array of categories yet
            if (isset($additional_categories['CategoryID']) || (isset($additional_categories[0]) && isset($additional_categories[0]['CategoryID']))) { 
                 // Check if the first element has CategoryID to determine if it's a list or a single item not yet in an array
                if (!is_array(current($additional_categories)) && isset($additional_categories['CategoryID'])) {
                     $additional_categories = [$additional_categories]; // Make it an array of one
                }
            }

            foreach ($additional_categories as $add_cat) {
                $add_cat_id = isset($add_cat['CategoryID']) ? $this->_getXmlNodeValue($add_cat['CategoryID']) : '';
                if(!empty($add_cat_id)) {
                    $opencart_product['categories_data_source'][] = [
                        'id' => $add_cat_id,
                        'name' => isset($add_cat['CategoryName']['BG']) ? $this->_getXmlNodeValue($add_cat['CategoryName']['BG']) : '',
                        'branch' => isset($add_cat['CategoryBranch']['BG']) ? $this->_getXmlNodeValue($add_cat['CategoryBranch']['BG']) : ''
                    ];
                }
            }
        }
    }

    private function _mapProductManufacturer(array &$opencart_product, array $supplier_product_data) {
        // $opencart_product['manufacturer_name_source'] е инициализиран в _initializeOpencartProduct
        $brand_name = isset($supplier_product_data['BrandName']) ? $this->_getXmlNodeValue($supplier_product_data['BrandName']) : '';
        $opencart_product['manufacturer_name_source'] = !empty($brand_name) ? $brand_name : '';
        // Ако BrandID е налично, може да се използва то. `doSync` ще търси/създава производител.
    }

    private function _mapProductAttributes(array &$opencart_product, array $supplier_product_data) {
        // Нужна е логика за намиране или създаване на атрибути и групи атрибути в Opencart.
        // Opencart очаква 'product_attribute' да е масив от обекти/масиви с attribute_id, language_id, text.
        // $opencart_product['attributes_data_source'] е инициализиран в _initializeOpencartProduct
        if (isset($supplier_product_data['ProductAttributeValues']['ProductAttributeValue'])) {
            $attributes_xml = $supplier_product_data['ProductAttributeValues']['ProductAttributeValue'];
            
            // Handle case where there's only one 'ProductAttributeValue' which is not an array of attributes yet
            if (isset($attributes_xml['AttributeCode'])) { 
                $attributes_xml = [$attributes_xml]; // Make it an array of one
            }

            foreach ($attributes_xml as $attr_xml) {
                $attr_name_xml = isset($attr_xml['AttributeCode']) ? $this->_getXmlNodeValue($attr_xml['AttributeCode']) : '';
                $attr_value_xml = '';
                if (isset($attr_xml['ProductAttributeValueOptionML']['BG'])) {
                    $attr_value_xml = $this->_getXmlNodeValue($attr_xml['ProductAttributeValueOptionML']['BG']);
                } elseif (isset($attr_xml['ProductAttributeValueText'])) {
                    $attr_value_xml = $this->_getXmlNodeValue($attr_xml['ProductAttributeValueText']);
                }

                if (!empty($attr_name_xml) && !empty($attr_value_xml)) {
                    $opencart_product['attributes_data_source'][] = [
                        'name'  => $attr_name_xml,
                        'value' => $attr_value_xml
                    ];
                }
            }
        }
    }

    private function _mapProductSeoData(array &$opencart_product, array $supplier_product_data) {
        // $opencart_product['meta_title'], ['meta_keyword'], ['meta_description'] са инициализирани в _initializeOpencartProduct
        // _mapBasicProductData може да е задал meta_title от името на продукта, ако SEO секцията липсва или е празна.
        if(isset($supplier_product_data['SEO'])) {
            $seo_title = isset($supplier_product_data['SEO']['Title']['BG']) ? $this->_getXmlNodeValue($supplier_product_data['SEO']['Title']['BG']) : '';
            if(!empty($seo_title)) {
                $opencart_product['meta_title'] = $seo_title;
            }
            
            $seo_keywords = isset($supplier_product_data['SEO']['MetaKeywords']['BG']) ? $this->_getXmlNodeValue($supplier_product_data['SEO']['MetaKeywords']['BG']) : '';
            if(!empty($seo_keywords)) {
                $opencart_product['meta_keyword'] = $seo_keywords;
            }
            
            $meta_desc_candidate = isset($supplier_product_data['SEO']['MetaDescription']['BG']) ? $this->_getXmlNodeValue($supplier_product_data['SEO']['MetaDescription']['BG']) : '';
            if(trim($meta_desc_candidate) !== '') {
                $opencart_product['meta_description'] = $meta_desc_candidate;
            } elseif (isset($supplier_product_data['ProductDescription']['BG']) && empty($opencart_product['meta_description'])) { 
                // Като алтернатива, вземи от описанието на продукта, само ако meta_description все още е празна
                $product_desc_for_meta = $this->_getXmlNodeValue($supplier_product_data['ProductDescription']['BG']);
                $opencart_product['meta_description'] = strip_tags(html_entity_decode($product_desc_for_meta));
                if(mb_strlen($opencart_product['meta_description']) > 255) {
                    $opencart_product['meta_description'] = mb_substr($opencart_product['meta_description'], 0, 252) . '...';
                }
            }
        }
    }

    private function _mapProductOptions(array &$opencart_product, array $supplier_product_data, array $full_feed_data) {
        // $opencart_product['options_data_source'] е инициализиран в _initializeOpencartProduct
        // Опции (Пример на база структурата OptionGroups, нуждае се от логика за свързване) [cite: 29, 30, 31, 32, 33]
        // Това е сложно, тъй като включва свързване на GlobalOptionGroupID с опциите на продукта.
        // XML предоставя OptionGroups на най-високо ниво. Продуктите може да реферират към тях.
        // Засега тази част е опростена. `doSync` ще се нуждае от стабилен начин за обработка.
        
        // Примерна логика (която трябва да се доразвие):
        // if (isset($supplier_product_data['ProductOptions']['ProductOption'])) {
        //     $product_options_xml = $supplier_product_data['ProductOptions']['ProductOption'];
        //     if (isset($product_options_xml['GlobalOptionGroupID'])) { // Единична опция
        //         $product_options_xml = [$product_options_xml];
        //     }

        //     foreach ($product_options_xml as $option_xml) {
        //         $global_option_group_id = (string)$option_xml['GlobalOptionGroupID'];
        //         // Търсене на $global_option_group_id в $full_feed_data['OptionGroups']
        //         // и мапиране на стойностите към $opencart_product['options_data_source'][]
        //         // или директно към $opencart_product['product_option'][] ако структурата позволява
        //     }
        // }
    }

    public function processSupplierFeed($c_key, $mfsc_id, &$current_log, $log_entry_prefix = '') {
        $log_entry_prefix = "Конектор ({$c_key}): ";
        $sync_stats = [
            'added' => 0, 
            'updated' => 0, 
            'skipped' => 0, 
            'errors' => 0, 
            'message' => '',
            'error_message' => '' // Ключ за грешки по време на процеса
        ];
        $connection_ok = false;

        // Стъпка 1 & 2: Извличане и подготовка на данните
        list($supplier_data_source, $data_is_xml, $connection_ok, $conversion_error) = $this->_requestAndPrepareData($c_key, $current_log, $sync_stats, $log_entry_prefix);
        $sync_stats['connection_established'] = $connection_ok;

        if (!$connection_ok || $conversion_error) {
            // Грешката вече е логната и записана в $sync_stats от _requestAndPrepareData
            return $sync_stats;
        }

        if (empty($supplier_data_source) && !$conversion_error) { // Проверка дали източникът на данни е празен, но не поради грешка при конверсия
            $current_log .= $log_entry_prefix . "Предупреждение: Източникът на данни е празен за {$c_key}.\n";
            $sync_stats['error_message'] = "Източникът на данни е празен";
            // Ако няма друга грешка, може да се добави и 'message'
            if (empty($sync_stats['message'])) {
                 $sync_stats['message'] = 'Източникът на данни е празен.';
            }
            return $sync_stats;
        }

        // Стъпка 3: Мапиране на продуктите (логиката от _mapProducts е интегрирана тук)
        $opencart_products_batch = [];
        $global_xml_data_array = []; // За глобални данни от XML като OptionGroups

        // Дефиниране на имената на XML възлите (специфични за eOffice или конфигурируеми)
        $xml_product_node_name = 'Product';
        $xml_products_parent_node_name = 'Products';
        $xml_option_groups_node_name = 'OptionGroups';
        // $xml_root_node_name = 'eOfficeFeed'; // Може да е нужен за по-стриктно парсиране

        if ($data_is_xml) {
            if (empty($supplier_data_source)) {
                $current_log .= $log_entry_prefix . "Грешка: XML източникът на данни е празен за {$c_key}.\n";
                $sync_stats['error_message'] = "XML източникът на данни е празен";
                return $sync_stats;
            }

            // 1. Извличане на глобални данни (OptionGroups)
            $xml_reader_global = new \XMLReader();
            if ($xml_reader_global->XML($supplier_data_source)) {
                while ($xml_reader_global->read()) {
                    if ($xml_reader_global->nodeType == \XMLReader::ELEMENT && $xml_reader_global->name == $xml_option_groups_node_name) {
                        $option_groups_xml_string = $xml_reader_global->readOuterXML();
                        if ($option_groups_xml_string) {
                            // Използваме оригиналния convertXMLdataToArray за малкия XML фрагмент
                            $global_data_candidate = $this->convertXMLdataToArray($option_groups_xml_string);
                            if ($global_data_candidate && is_array($global_data_candidate)){
                                $global_xml_data_array = $global_data_candidate; // Цялата структура, върната от convertXMLdataToArray
                            }
                        }
                        break; // Намерихме OptionGroups, приемаме, че е само една такава секция
                    }
                }
                $xml_reader_global->close();
            } else {
                $current_log .= $log_entry_prefix . "Грешка: Неуспешно зареждане на XML за извличане на глобални данни ({$xml_option_groups_node_name}) за {$c_key}.\n";
                // Продължаваме без глобални данни, ако е възможно, или маркираме като грешка
            }

            // 2. Поточно четене и обработка на продуктите
            $xml_reader_products = new \XMLReader();
            if (!$xml_reader_products->XML($supplier_data_source)) {
                $current_log .= $log_entry_prefix . "Грешка: Неуспешно зареждане на XML за обработка на продукти за {$c_key}.\n";
                $sync_stats['error_message'] = "Неуспешно зареждане на XML за продукти";
                return $sync_stats;
            }

            $found_products_parent = false;
            // Навигиране до родителския елемент на продуктите
            while ($xml_reader_products->read() && !($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == $xml_products_parent_node_name)) {
                // Продължаваме да четем, докато не намерим $xml_products_parent_node_name
            }

            if ($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == $xml_products_parent_node_name) {
                $found_products_parent = true;
            }

            if ($found_products_parent) {
                // Обхождане на всеки продукт вътре в родителския елемент
                while ($xml_reader_products->read()) {
                    if ($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == $xml_product_node_name) {
                        $node = @$xml_reader_products->expand(); // @ за потискане на грешки при expand, ако има такива
                        if ($node) {
                            $supplier_product_data = $this->_convertDomNodeToArray($node);
                            if ($supplier_product_data && is_array($supplier_product_data)) {
                                $opencart_product_data = $this->mapDataToOpencartProduct($supplier_product_data, $global_xml_data_array);
                                if ($opencart_product_data) {
                                    $opencart_products_batch[] = $opencart_product_data;
                                } else {
                                    $sync_stats['skipped']++;
                                    $current_log .= $log_entry_prefix . "Продукт пропуснат по време на мапиране. Данни: " . mb_substr(print_r($supplier_product_data, true), 0, 200) . "...\n";
                                }
                            } else {
                                $current_log .= $log_entry_prefix . "Грешка: Неуспешно конвертиране на продуктов XML възел в масив.\n";
                                $sync_stats['errors']++;
                            }
                        }
                    } elseif ($xml_reader_products->nodeType == \XMLReader::END_ELEMENT && $xml_reader_products->name == $xml_products_parent_node_name) {
                        break; // Край на родителския елемент за продукти
                    }
                }
            } else {
                $current_log .= $log_entry_prefix . "Предупреждение: Не е намерен родителски елемент '{$xml_products_parent_node_name}' за продуктите в XML фийда за {$c_key}.\n";
            }
            $xml_reader_products->close();

        } elseif (!$data_is_xml && is_array($supplier_data_source)) { // Обработка на JSON или друг масив
            if (!method_exists($this, 'mapDataToOpencartProduct')) {
                $current_log .= $log_entry_prefix . "Грешка: Методът mapDataToOpencartProduct липсва в {$c_key} конектора.\n";
                $sync_stats['error_message'] = "Методът mapDataToOpencartProduct липсва";
                return $sync_stats; // Връщаме $sync_stats, тъй като $opencart_products_batch ще е празен
            }

            $products_from_feed = $supplier_data_source; // Приемаме, че $supplier_data_source е масив от продукти

            if (empty($products_from_feed)) {
                $current_log .= $log_entry_prefix . "Няма намерени продукти във фийда (JSON/масив) за {$c_key} за мапиране.\n";
                if (empty($sync_stats['error_message'])) {
                    $sync_stats['message'] = 'Няма продукти във фийда (JSON/масив) за мапиране';
                }
            } else {
                foreach ($products_from_feed as $product_data_item) {
                    if (!is_array($product_data_item)) {
                        $current_log .= $log_entry_prefix . "Предупреждение: Елемент от фийда (JSON/масив) не е масив и ще бъде пропуснат. Данни: " . print_r($product_data_item, true) . "\n";
                        $sync_stats['skipped']++;
                        continue;
                    }
                    // При JSON, $supplier_data_source е целият feed, който се подава като $full_feed_data
                    $opencart_product_data = $this->mapDataToOpencartProduct($product_data_item, $supplier_data_source);
                    if ($opencart_product_data) {
                        $opencart_products_batch[] = $opencart_product_data;
                    }
                }
            }
        } else {
            if (!$connection_ok || ($data_is_xml && empty($supplier_data_source))) { 
                // Грешката вече е обработена или $supplier_data_source е празен XML, което също е проблем
            } else {
                $current_log .= $log_entry_prefix . "Грешка: Неочакван тип на данните или празен източник след _requestAndPrepareData за {$c_key}. Тип XML: " . ($data_is_xml ? 'Да' : 'Не') . ", Данни: " . print_r($supplier_data_source, true) ."\n";
                $sync_stats['error_message'] = "Неочакван тип на данните или празен източник след подготовка.";
            }
            // Връщаме $sync_stats, тъй като $opencart_products_batch ще е празен
            return $sync_stats; 
        }

        $current_log .= $log_entry_prefix . count($opencart_products_batch) . " продукта са мапирани за {$c_key}.\n";

        if (empty($opencart_products_batch)) {
            if (empty($sync_stats['error_message']) && empty($sync_stats['message'])) { // Ако няма специфична грешка или съобщение от мапирането
                $current_log .= $log_entry_prefix . "Няма продукти за обработка от фийда за {$c_key} след мапиране.\n";
                $sync_stats['message'] = 'Няма продукти във фийда за обработка';
            }
            // Грешката (ако има) вече е логната и записана в $sync_stats
            return $sync_stats;
        }

            // Стъпка 4: Синхронизиране на продуктите
            $sync_result_stats = $this->_syncProducts($opencart_products_batch, $mfsc_id, $c_key, $current_log, $log_entry_prefix);
            
            // Обединяване на статистики - doSync връща основната статистика
            // Запазваме error_message ако има такова от предишните стъпки
            $final_error_message = $sync_stats['error_message'];
            $sync_stats = array_merge($sync_stats, $sync_result_stats);
            if (!empty($final_error_message)) {
                $sync_stats['error_message'] = $final_error_message . (isset($sync_result_stats['error_message']) && !empty($sync_result_stats['error_message']) ? '; ' . $sync_result_stats['error_message'] : '');
            } elseif (isset($sync_result_stats['error_message'])) {
                $sync_stats['error_message'] = $sync_result_stats['error_message'];
            }

            return $sync_stats;
        }

        public function getCachedSyncDataFilePath(){
            return DIR_CACHE . 'eoffice_feed_data.xml';
        }

        private function requestCachedSyncData(){
            $cached_data_file = $this->getCachedSyncDataFilePath();
            if(file_exists($cached_data_file)){
                return file_get_contents($cached_data_file);
            }
            return false;
        }

        private function setCachedSyncData($raw_data){
            $cached_data_file = $this->getCachedSyncDataFilePath();
            file_put_contents($cached_data_file, $raw_data);
        }

        private function _requestAndPrepareData($c_key, &$current_log, &$sync_stats, $log_entry_prefix) {
            $raw_data = $this->requestCachedSyncData(); 
            if(!$raw_data){
                $raw_data = $this->requestSyncData(); 
                $this->setCachedSyncData($raw_data);
            }
            $connection_ok = false;
            $conversion_error = false; // Ще се използва само за JSON тук
            $supplier_data_source = null; // Може да бъде XML string или PHP array
            $data_is_xml = false;

            if (!$raw_data) {
                $current_log .= $log_entry_prefix . "Грешка: Неуспешно извличане на данни за {$c_key}.\n";
                $sync_stats['error_message'] = "Неуспешно извличане на данни";
                return [$supplier_data_source, $data_is_xml, $connection_ok, $conversion_error];
            }

            $connection_ok = true;
            $current_log .= $log_entry_prefix . "Данните са извлечени успешно за {$c_key}.\n";

            $data_is_xml = stripos($this->getConnectionType(), 'XML') !== false;

            if ($data_is_xml) {
                $supplier_data_source = $raw_data; // Връщаме суровия XML низ
                // Конверсията ще се прави на парче в processSupplierFeed
                $current_log .= $log_entry_prefix . "XML данните са получени и ще бъдат обработени поточно за {$c_key}.\n";
            } else { // Обработка на JSON или други типове данни, които се конвертират изцяло
                $decoded_json = json_decode($raw_data, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $supplier_data_source = $decoded_json;
                    $current_log .= $log_entry_prefix . "Данните (JSON) са конвертирани в масив за {$c_key}.\n";
                } else {
                    if (is_array($raw_data)) {
                        $supplier_data_source = $raw_data;
                        $current_log .= $log_entry_prefix . "Данните са получени като масив за {$c_key}.\n";
                    } else {
                        $current_log .= $log_entry_prefix . "Предупреждение: Данните за {$c_key} не са XML и не са валиден JSON. Пропускане на конверсията.\n";
                        $sync_stats['error_message'] = "Неподдържан формат на данните или грешка при конверсия";
                        $conversion_error = true;
                    }
                }
            }
            return [$supplier_data_source, $data_is_xml, $connection_ok, $conversion_error];
    }

    /**
     * Синхронизира партида от OpenCart продукти с базата данни.
     * @param array $opencart_products_batch Батч от продукти за синхронизация.
     * @param int $mfsc_id ID на настройката за синхронизация.
     * @param string $c_key Ключ на конектора.
     * @param string &$current_log Текущия лог.
     * @param string $log_entry_prefix Префикс за лог записите.
     * @return array Статистика за синхронизацията.
     */
    private function _syncProducts($opencart_products_batch, $mfsc_id, $c_key, &$current_log, $log_entry_prefix) {
        if (!$this->model_extension_module_multi_feed_syncer) {
             $current_log .= $log_entry_prefix . "Грешка: Основният модел 'multi_feed_syncer' не е зареден преди извикване на _syncProducts.\n";
             // Зареждане на модела, ако липсва - това е добра практика, въпреки че би трябвало да е зареден от извикващия код.
             $this->load->model('extension/module/multi_feed_syncer');

             $debug_message = "MultiFeed Syncer (Test Mode): _syncProducts() called, model_extension_module_multi_feed_syncer is " . gettype($this->model_extension_module_multi_feed_syncer);
             

             if (!is_object($this->model_extension_module_multi_feed_syncer)) {
                 
                 $current_log .= $log_entry_prefix . "КРИТИЧНА ГРЕШКА: Основният модел 'model_extension_module_multi_feed_syncer' не успя да се зареди или не е обект в _syncProducts.\n";
                 return ['added' => 0, 'updated' => 0, 'skipped' => 0, 'errors' => count($opencart_products_batch), 'error_message' => 'Основният модел multi_feed_syncer не можа да бъде зареден или не е обект. ' . $debug_message];

             } elseif (!is_callable([$this->model_extension_module_multi_feed_syncer, 'doSync'])) {
                 
                 $current_log .= $log_entry_prefix . "КРИТИЧНА ГРЕШКА: Методът 'doSync' не съществува в заредения модел 'model_extension_module_multi_feed_syncer'.\n";
                 return ['added' => 0, 'updated' => 0, 'skipped' => 0, 'errors' => count($opencart_products_batch), 'error_message' => "Методът 'doSync' не е намерен в основния модел. " . $debug_message];

             } else {
                 $current_log .= $log_entry_prefix . "ИНФО: Основният модел 'model_extension_module_multi_feed_syncer' и методът 'doSync' са налични в _syncProducts.\n";
             }
        }

        $sync_stats = $this->model_extension_module_multi_feed_syncer->doSync($opencart_products_batch, $mfsc_id);
        $current_log .= $log_entry_prefix . "doSync е завършен за {$c_key}. Статистика: " . print_r($sync_stats, true) . "\n";
        return $sync_stats;
    }

    /**
     * Рекурсивно конвертира DOMNode обект в PHP масив.
     *
     * @param DOMNode $node DOMNode за конвертиране.
     * @return array|string Конвертираният масив или стринг, ако е текстов възел.
     */
    private function _convertDomNodeToArray(DOMNode $node) {
        $output = [];
        switch ($node->nodeType) {
            case XML_CDATA_SECTION_NODE:
            case XML_TEXT_NODE:
                $output = trim($node->textContent);
                break;

            case XML_ELEMENT_NODE:
                for ($i = 0, $m = $node->childNodes->length; $i < $m; $i++) {
                    $child = $node->childNodes->item($i);
                    $v = $this->_convertDomNodeToArray($child);
                    if (isset($child->tagName)) {
                        $t = $child->tagName;
                        if (!isset($output[$t])) {
                            $output[$t] = [];
                        }
                        $output[$t][] = $v;
                    } elseif ($v !== '' && $v !== null) {
                         $output[] = $v;
                    }
                }

                if ($node->attributes->length && !empty($output)) {
                    $attributes = [];
                    foreach ($node->attributes as $attrName => $attrNode) {
                        $attributes[$attrName] = (string)$attrNode->value;
                    }
                    if (!is_array($output)) {
                        $output = ['#text' => $output];
                    }
                    $output['@attributes'] = $attributes;
                } elseif ($node->attributes->length) {
                     $output = [];
                     foreach ($node->attributes as $attrName => $attrNode) {
                        $output['@attributes'][$attrName] = (string)$attrNode->value;
                    }
                }

                if (is_array($output)) {
                    foreach ($output as $t => $v) {
                        if (is_array($v) && count($v) == 1 && $t != '@attributes') {
                            if (array_key_exists(0, $v)) {
                                $output[$t] = $v[0];
                            }
                        }
                    }
                    if (empty($output) && !$node->attributes->length && !$node->hasChildNodes()) {
                        $output = '';
                    } elseif (empty($output) && $node->attributes->length && !$node->hasChildNodes()){
                        // Handled above
                    }
                }
                break;
        }
        
        if (is_array($output) && empty($output) && $node->nodeType == XML_ELEMENT_NODE && !$node->attributes->length) {
            $hasRealText = false;
            if ($node->hasChildNodes()) {
                for ($i=0; $i < $node->childNodes->length; $i++) {
                    if ($node->childNodes->item($i)->nodeType == XML_TEXT_NODE && trim($node->childNodes->item($i)->textContent) !== '') {
                        $hasRealText = true;
                        break;
                    }
                }
            }
            if (!$hasRealText) {
                return ''; 
            }
        }

        return $output;
    }

    /**
     * Връща данни за конкретен лог запис, които да се покажат в таб "Логове". [cite: 23]
     * Главният контролер ще извика този метод, подавайки десериализираните `process_data` от лога.
     * @param array $log_process_data Десериализирани данни от колоната `multi_feed_syncer_logs.process_data`.
     * @return string Форматиран низ или структурирани данни за показване.
     */
    public function getSyncLogData(array $log_process_data) { 
        $info_parts = [];
        if (isset($log_process_data['received'])) {
            $info_parts[] = "Получени: " . (int)$log_process_data['received'];
        }
        if (isset($log_process_data['added'])) {
            $info_parts[] = "Добавени: " . (int)$log_process_data['added'];
        }
        if (isset($log_process_data['updated'])) {
            $info_parts[] = "Актуализирани: " . (int)$log_process_data['updated'];
        }
        if (isset($log_process_data['skipped'])) {
            $info_parts[] = "Пропуснати: " . (int)$log_process_data['skipped'];
        }
        if (isset($log_process_data['errors'])) {
            $info_parts[] = "Грешки: " . (int)$log_process_data['errors'];
        }
        if (isset($log_process_data['message'])) {
            $info_parts[] = "Съобщение: " . htmlspecialchars($log_process_data['message']);
        }
         if (isset($log_process_data['error_message'])) {
            $info_parts[] = "Съобщение за грешка: " . htmlspecialchars($log_process_data['error_message']);
        }
        // Структурата на $log_process_data ще бъде базирана на това, което `doSync` връща и записва.
        return implode(', ', $info_parts);
    }
}
?>