<?php
/**
 * Пълен тест на процеса за запазване на кавички в eOffice конектора
 * Тества цялата верига: XML → конектор → основен модул → база данни → интерфейс
 */

// Включваме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Зареждаме config
$config = new Config();
$registry->set('config', $config);

// Зареждаме модела
$loader = new Loader($registry);
$registry->set('load', $loader);

// Зареждаме eOffice конектора
$loader->model('extension/module/multi_feed_syncer_connectors/eoffice');
$eoffice_connector = $registry->get('model_extension_module_multi_feed_syncer_connectors_eoffice');

// Зареждаме основния Multi Feed Syncer модел
$loader->model('extension/module/multi_feed_syncer');
$main_model = $registry->get('model_extension_module_multi_feed_syncer');

echo "<h1>Пълен тест на процеса за запазване на кавички в eOffice конектора</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ccc; }
    .code { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
</style>\n";

// Тестови XML данни със специални символи
$test_xml_data = '<?xml version="1.0" encoding="UTF-8"?>
<Products>
    <Product>
        <ProductCode>TEST001</ProductCode>
        <ProductName>
            <BG>Монитор Samsung 24" LED Full HD</BG>
        </ProductName>
        <Category>
            <CategoryID>1</CategoryID>
            <CategoryName>
                <BG>Монитори</BG>
            </CategoryName>
            <CategoryBranch>
                <BG>Компютри|Монитори|24" LED монитори</BG>
            </CategoryBranch>
        </Category>
        <AdditionalCategories>
            <Category>
                <CategoryID>2</CategoryID>
                <CategoryName>
                    <BG>Gaming монитори</BG>
                </CategoryName>
                <CategoryBranch>
                    <BG>Gaming|Монитори 24"|Samsung модели</BG>
                </CategoryBranch>
            </Category>
        </AdditionalCategories>
        <ProductDescription>
            <BG>Висококачествен 24" LED монитор с Full HD резолюция</BG>
        </ProductDescription>
        <ProductPrice>299.99</ProductPrice>
        <ProductQuantity>10</ProductQuantity>
        <ProductIsActive>yes</ProductIsActive>
    </Product>
    <Product>
        <ProductCode>TEST002</ProductCode>
        <ProductName>
            <BG>Телевизор LG 55" Smart TV</BG>
        </ProductName>
        <Category>
            <CategoryID>3</CategoryID>
            <CategoryName>
                <BG>Телевизори</BG>
            </CategoryName>
            <CategoryBranch>
                <BG>Електроника|Телевизори|55" Smart TV</BG>
            </CategoryBranch>
        </Category>
        <ProductDescription>
            <BG>Модерен 55" Smart TV с 4K резолюция</BG>
        </ProductDescription>
        <ProductPrice>899.99</ProductPrice>
        <ProductQuantity>5</ProductQuantity>
        <ProductIsActive>yes</ProductIsActive>
    </Product>
</Products>';

echo "<div class='test-section'>\n";
echo "<h2>Тест 1: Конвертиране на XML данни</h2>\n";

// Тестваме конвертирането на XML
$converted_data = $eoffice_connector->convertXMLdataToArray($test_xml_data);

echo "<h3>Оригинални XML данни:</h3>\n";
echo "<div class='code'>" . htmlspecialchars($test_xml_data) . "</div>\n";

echo "<h3>Конвертирани данни:</h3>\n";
echo "<div class='code'>" . htmlspecialchars(print_r($converted_data, true)) . "</div>\n";

// Проверяваме дали кавичките се запазват
$product1_name = $converted_data['Product'][0]['ProductName']['BG'] ?? '';
$product1_category = $converted_data['Product'][0]['Category']['CategoryBranch']['BG'] ?? '';

echo "<table>\n";
echo "<tr><th>Поле</th><th>Стойност</th><th>Съдържа кавички</th><th>Статус</th></tr>\n";
echo "<tr>\n";
echo "<td>Име на продукт 1</td>\n";
echo "<td>" . htmlspecialchars($product1_name) . "</td>\n";
echo "<td>" . (strpos($product1_name, '"') !== false ? 'ДА' : 'НЕ') . "</td>\n";
echo "<td class='" . (strpos($product1_name, '"') !== false ? 'success' : 'error') . "'>" . (strpos($product1_name, '"') !== false ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>Категория на продукт 1</td>\n";
echo "<td>" . htmlspecialchars($product1_category) . "</td>\n";
echo "<td>" . (strpos($product1_category, '"') !== false ? 'ДА' : 'НЕ') . "</td>\n";
echo "<td class='" . (strpos($product1_category, '"') !== false ? 'success' : 'error') . "'>" . (strpos($product1_category, '"') !== false ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
echo "</tr>\n";
echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 2: Извличане на категории от файла</h2>\n";

// Симулираме кеширани данни
$cache_file = $eoffice_connector->getCachedSyncDataFilePath();
$cache_dir = dirname($cache_file);
if (!is_dir($cache_dir)) {
    mkdir($cache_dir, 0755, true);
}
file_put_contents($cache_file, $test_xml_data);

// Извличаме категориите
$categories_from_file = $eoffice_connector->getCategoriesFromFile();

echo "<h3>Извлечени категории:</h3>\n";
echo "<table>\n";
echo "<tr><th>Категория</th><th>Съдържа кавички</th><th>Статус</th></tr>\n";

foreach ($categories_from_file as $category) {
    $has_quotes = strpos($category, '"') !== false;
    echo "<tr>\n";
    echo "<td>" . htmlspecialchars($category) . "</td>\n";
    echo "<td>" . ($has_quotes ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='" . ($has_quotes ? 'success' : 'warning') . "'>" . ($has_quotes ? 'УСПЕХ' : 'БЕЗ КАВИЧКИ') . "</td>\n";
    echo "</tr>\n";
}
echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 3: Запазване в основния модул</h2>\n";

$test_mfsc_id = 999;
$test_cases = [];

// Подготвяме тестови случаи от извлечените категории
foreach ($categories_from_file as $category) {
    if (strpos($category, '"') !== false) {
        $test_cases[] = [
            'source' => $category,
            'target' => 'TV, Монитори, Видео и Аудио'
        ];
    }
}

// Изчистваме стари тестови данни
$db->query("DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");

echo "<table>\n";
echo "<tr><th>Source категория</th><th>Target категория</th><th>Mapping ID</th><th>Статус</th></tr>\n";

foreach ($test_cases as $test_case) {
    try {
        $mapping_id = $main_model->saveCategoryMapping($test_mfsc_id, $test_case['source'], $test_case['target']);
        
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
        echo "<td>" . htmlspecialchars($test_case['target']) . "</td>\n";
        echo "<td>{$mapping_id}</td>\n";
        echo "<td class='success'>УСПЕХ</td>\n";
        echo "</tr>\n";
    } catch (Exception $e) {
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
        echo "<td>" . htmlspecialchars($test_case['target']) . "</td>\n";
        echo "<td>-</td>\n";
        echo "<td class='error'>ГРЕШКА: " . htmlspecialchars($e->getMessage()) . "</td>\n";
        echo "</tr>\n";
    }
}
echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 4: Извличане от основния модул</h2>\n";

$retrieved_mappings = $main_model->getCategoriesMapping($test_mfsc_id);

echo "<table>\n";
echo "<tr><th>Source категория</th><th>Target категория</th><th>Кавички запазени</th><th>Статус</th></tr>\n";

foreach ($retrieved_mappings as $mapping) {
    $has_quotes = strpos($mapping['source_category_path'], '"') !== false;
    
    echo "<tr>\n";
    echo "<td>" . htmlspecialchars($mapping['source_category_path']) . "</td>\n";
    echo "<td>" . htmlspecialchars($mapping['target_category_path']) . "</td>\n";
    echo "<td>" . ($has_quotes ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='" . ($has_quotes ? 'success' : 'error') . "'>" . ($has_quotes ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
    echo "</tr>\n";
}
echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 5: Проверка в базата данни</h2>\n";

$query = $db->query("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");

echo "<table>\n";
echo "<tr><th>Source в базата</th><th>Target в базата</th><th>Кавички в Source</th><th>Статус</th></tr>\n";

foreach ($query->rows as $row) {
    $has_quotes = strpos($row['source_category_path'], '"') !== false;
    
    echo "<tr>\n";
    echo "<td>" . htmlspecialchars($row['source_category_path']) . "</td>\n";
    echo "<td>" . htmlspecialchars($row['target_category_path']) . "</td>\n";
    echo "<td>" . ($has_quotes ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='" . ($has_quotes ? 'success' : 'error') . "'>" . ($has_quotes ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
    echo "</tr>\n";
}
echo "</table>\n";
echo "</div>\n";

// Изчистваме тестовите данни
$db->query("DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");
if (file_exists($cache_file)) {
    unlink($cache_file);
}

echo "<div class='test-section'>\n";
echo "<h2>Заключение</h2>\n";
echo "<p>Този тест проверява цялостния процес за запазване на кавички в eOffice конектора:</p>\n";
echo "<ul>\n";
echo "<li><strong>XML конвертиране:</strong> Дали специалните символи се запазват при конвертиране от XML</li>\n";
echo "<li><strong>Извличане на категории:</strong> Дали getCategoriesFromFile() запазва кавичките</li>\n";
echo "<li><strong>Запазване в модула:</strong> Дали основният модул запазва кавичките</li>\n";
echo "<li><strong>Извличане от модула:</strong> Дали се четат правилно от основния модул</li>\n";
echo "<li><strong>Съхранение в базата:</strong> Дали се съхраняват правилно в базата данни</li>\n";
echo "</ul>\n";
echo "<p>Ако всички тестове показват 'УСПЕХ', корекцията работи правилно в цялата верига.</p>\n";
echo "</div>\n";

?>
