<?php
/**
 * Директен тест на XML обработката за запазване на кавички
 * Тества различни методи за обработка на XML данни
 */

// Включваме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Зареждаме config
$config = new Config();
$registry->set('config', $config);

// Зареждаме модела
$loader = new Loader($registry);
$registry->set('load', $loader);

// Зареждаме eOffice конектора
$loader->model('extension/module/multi_feed_syncer_connectors/eoffice');
$eoffice_connector = $registry->get('model_extension_module_multi_feed_syncer_connectors_eoffice');

echo "<h1>Директен тест на XML обработката за запазване на кавички</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ccc; }
    .code { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
</style>\n";

// Тестови XML фрагменти със специални символи
$test_xml_fragments = [
    'Прост текст с кавички' => '<CategoryBranch><BG>Монитори > 24" LED монитори > Samsung</BG></CategoryBranch>',
    'HTML entities' => '<CategoryBranch><BG>Монитори &gt; 24&quot; LED монитори &gt; Samsung</BG></CategoryBranch>',
    'Смесени символи' => '<CategoryBranch><BG>Телевизори > 55" Smart TV & 4K модели</BG></CategoryBranch>',
    'Единични кавички' => '<CategoryBranch><BG>Принтери > A4 \'All-in-One\' устройства</BG></CategoryBranch>',
    'CDATA секция' => '<CategoryBranch><BG><![CDATA[Монитори > 24" LED монитори > Samsung]]></BG></CategoryBranch>',
    'Множество кавички' => '<CategoryBranch><BG>Компютри > 15.6" лаптопи > "Gaming" серия</BG></CategoryBranch>'
];

echo "<div class='test-section'>\n";
echo "<h2>Тест 1: Различни методи за XML обработка</h2>\n";

echo "<table>\n";
echo "<tr><th>Тестов случай</th><th>Оригинален XML</th><th>Конвертиран резултат</th><th>Кавички запазени</th><th>Статус</th></tr>\n";

foreach ($test_xml_fragments as $test_name => $xml_fragment) {
    // Обвиваме във валиден XML документ
    $full_xml = '<?xml version="1.0" encoding="UTF-8"?>' . $xml_fragment;
    
    // Конвертираме с нашия метод
    $converted = $eoffice_connector->convertXMLdataToArray($full_xml);
    
    // Извличаме стойността
    $extracted_value = '';
    if (isset($converted['CategoryBranch']['BG'])) {
        $extracted_value = $converted['CategoryBranch']['BG'];
    }
    
    // Проверяваме дали кавичките се запазват
    $original_has_quotes = strpos($xml_fragment, '"') !== false || strpos($xml_fragment, '&quot;') !== false;
    $result_has_quotes = strpos($extracted_value, '"') !== false;
    
    $status_class = 'success';
    $status_text = 'УСПЕХ';
    
    if ($original_has_quotes && !$result_has_quotes) {
        $status_class = 'error';
        $status_text = 'ГРЕШКА - Кавички загубени';
    } elseif (!$original_has_quotes && !$result_has_quotes) {
        $status_class = 'warning';
        $status_text = 'БЕЗ КАВИЧКИ';
    }
    
    echo "<tr>\n";
    echo "<td><strong>" . htmlspecialchars($test_name) . "</strong></td>\n";
    echo "<td><div class='code'>" . htmlspecialchars($xml_fragment) . "</div></td>\n";
    echo "<td><div class='code'>" . htmlspecialchars($extracted_value) . "</div></td>\n";
    echo "<td>" . ($result_has_quotes ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='{$status_class}'>{$status_text}</td>\n";
    echo "</tr>\n";
}
echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 2: Сравнение на различни PHP методи</h2>\n";

$test_xml = '<?xml version="1.0" encoding="UTF-8"?><CategoryBranch><BG>Монитори > 24" LED монитори > Samsung</BG></CategoryBranch>';

echo "<h3>Тестов XML:</h3>\n";
echo "<div class='code'>" . htmlspecialchars($test_xml) . "</div>\n";

echo "<table>\n";
echo "<tr><th>Метод</th><th>Резултат</th><th>Кавички запазени</th><th>Статус</th></tr>\n";

// Метод 1: Стандартен simplexml_load_string
try {
    $xml1 = simplexml_load_string($test_xml);
    $result1 = (string)$xml1->BG;
    $quotes1 = strpos($result1, '"') !== false;
    
    echo "<tr>\n";
    echo "<td>simplexml_load_string (стандартен)</td>\n";
    echo "<td>" . htmlspecialchars($result1) . "</td>\n";
    echo "<td>" . ($quotes1 ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='" . ($quotes1 ? 'success' : 'error') . "'>" . ($quotes1 ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
    echo "</tr>\n";
} catch (Exception $e) {
    echo "<tr><td>simplexml_load_string (стандартен)</td><td>ГРЕШКА</td><td>НЕ</td><td class='error'>ГРЕШКА</td></tr>\n";
}

// Метод 2: simplexml_load_string с LIBXML_NOCDATA
try {
    $xml2 = simplexml_load_string($test_xml, "SimpleXMLElement", LIBXML_NOCDATA);
    $result2 = (string)$xml2->BG;
    $quotes2 = strpos($result2, '"') !== false;
    
    echo "<tr>\n";
    echo "<td>simplexml_load_string (LIBXML_NOCDATA)</td>\n";
    echo "<td>" . htmlspecialchars($result2) . "</td>\n";
    echo "<td>" . ($quotes2 ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='" . ($quotes2 ? 'success' : 'error') . "'>" . ($quotes2 ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
    echo "</tr>\n";
} catch (Exception $e) {
    echo "<tr><td>simplexml_load_string (LIBXML_NOCDATA)</td><td>ГРЕШКА</td><td>НЕ</td><td class='error'>ГРЕШКА</td></tr>\n";
}

// Метод 3: Нашия подобрен метод
try {
    $result3_array = $eoffice_connector->convertXMLdataToArray($test_xml);
    $result3 = $result3_array['CategoryBranch']['BG'] ?? '';
    $quotes3 = strpos($result3, '"') !== false;
    
    echo "<tr>\n";
    echo "<td>Нашия подобрен метод</td>\n";
    echo "<td>" . htmlspecialchars($result3) . "</td>\n";
    echo "<td>" . ($quotes3 ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='" . ($quotes3 ? 'success' : 'error') . "'>" . ($quotes3 ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
    echo "</tr>\n";
} catch (Exception $e) {
    echo "<tr><td>Нашия подобрен метод</td><td>ГРЕШКА: " . htmlspecialchars($e->getMessage()) . "</td><td>НЕ</td><td class='error'>ГРЕШКА</td></tr>\n";
}

// Метод 4: DOMDocument
try {
    $dom = new DOMDocument();
    $dom->loadXML($test_xml);
    $result4 = $dom->getElementsByTagName('BG')->item(0)->nodeValue;
    $quotes4 = strpos($result4, '"') !== false;
    
    echo "<tr>\n";
    echo "<td>DOMDocument</td>\n";
    echo "<td>" . htmlspecialchars($result4) . "</td>\n";
    echo "<td>" . ($quotes4 ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td class='" . ($quotes4 ? 'success' : 'error') . "'>" . ($quotes4 ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
    echo "</tr>\n";
} catch (Exception $e) {
    echo "<tr><td>DOMDocument</td><td>ГРЕШКА</td><td>НЕ</td><td class='error'>ГРЕШКА</td></tr>\n";
}

echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 3: JSON encoding/decoding влияние</h2>\n";

$test_string = 'Монитори > 24" LED монитори > Samsung';

echo "<h3>Оригинален стринг:</h3>\n";
echo "<div class='code'>" . htmlspecialchars($test_string) . "</div>\n";

echo "<table>\n";
echo "<tr><th>Операция</th><th>Резултат</th><th>Кавички запазени</th><th>Статус</th></tr>\n";

// Стандартен JSON encode/decode
$json_encoded = json_encode($test_string);
$json_decoded = json_decode($json_encoded);
$quotes_json = strpos($json_decoded, '"') !== false;

echo "<tr>\n";
echo "<td>json_encode/decode (стандартен)</td>\n";
echo "<td>" . htmlspecialchars($json_decoded) . "</td>\n";
echo "<td>" . ($quotes_json ? 'ДА' : 'НЕ') . "</td>\n";
echo "<td class='" . ($quotes_json ? 'success' : 'error') . "'>" . ($quotes_json ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
echo "</tr>\n";

// JSON encode/decode с флагове
$json_encoded_flags = json_encode($test_string, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
$json_decoded_flags = json_decode($json_encoded_flags);
$quotes_json_flags = strpos($json_decoded_flags, '"') !== false;

echo "<tr>\n";
echo "<td>json_encode/decode (с флагове)</td>\n";
echo "<td>" . htmlspecialchars($json_decoded_flags) . "</td>\n";
echo "<td>" . ($quotes_json_flags ? 'ДА' : 'НЕ') . "</td>\n";
echo "<td class='" . ($quotes_json_flags ? 'success' : 'error') . "'>" . ($quotes_json_flags ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
echo "</tr>\n";

// HTML entity decode
$html_decoded = html_entity_decode($test_string, ENT_QUOTES | ENT_HTML5, 'UTF-8');
$quotes_html = strpos($html_decoded, '"') !== false;

echo "<tr>\n";
echo "<td>html_entity_decode</td>\n";
echo "<td>" . htmlspecialchars($html_decoded) . "</td>\n";
echo "<td>" . ($quotes_html ? 'ДА' : 'НЕ') . "</td>\n";
echo "<td class='" . ($quotes_html ? 'success' : 'error') . "'>" . ($quotes_html ? 'УСПЕХ' : 'ГРЕШКА') . "</td>\n";
echo "</tr>\n";

echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Заключение</h2>\n";
echo "<p>Този тест проверява различни методи за обработка на XML данни и тяхното влияние върху специални символи:</p>\n";
echo "<ul>\n";
echo "<li><strong>XML конвертиране:</strong> Как различните методи запазват кавичките</li>\n";
echo "<li><strong>JSON обработка:</strong> Влиянието на JSON encode/decode върху специални символи</li>\n";
echo "<li><strong>HTML декодиране:</strong> Ефективността на html_entity_decode</li>\n";
echo "</ul>\n";
echo "<p>Най-добрият метод трябва да запазва всички специални символи включително кавичките.</p>\n";
echo "</div>\n";

?>
