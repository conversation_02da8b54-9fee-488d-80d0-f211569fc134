<?php
class ModelExtensionModuleMultiFeedSyncerImageDownloader extends Model {

    private $downloadSubdir = '';
    private $log;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->log = new Log('multi_feed_syncer_image_downloader.log');
    }

    /**
     * Задава името на подпапката за изтегляне на изображения.
     *
     * @param string $subdirName Името на папката (напр. 'eoffice').
     */
    public function setDownloadSubdir($subdirName) {
        // Изчистваме името на папката от нежелани символи за сигурност
        $sanitizedDir = trim(preg_replace('/[^a-zA-Z0-9_-]/', '', $subdirName));

        // Уверяваме се, че името не е празно след изчистването
        if (!empty($sanitizedDir)) {
            $this->downloadSubdir = $sanitizedDir . '/';
        }
    }

    public function getMainDownloadPath() {
        return DIR_IMAGE . 'catalog/';
    }

    /**
     * Изтегля изображения от списък с URL адреси последователно, записва ги локално и връща карта на съответствията.
     * Изтегля по едно изображение на всеки 3 секунди за намаляване на натоварването върху сървъра.
     *
     * @param array $image_urls Масив от уникални URL адреси на изображения за изтегляне.
     * @return array Асоциативен масив [original_url => new_relative_path].
     */
    public function downloadAndProcessImages($image_urls, $subdirName=null) {
        if (empty($image_urls)) {
            return [];
        }

        if($subdirName) {
            $this->setDownloadSubdir($subdirName);
        }

        $this->_ensureDirectoryExists();
        
        $downloaded_map = [];
        $download_count = 0;

        if (isset($this->log)) {
            $this->log->write("Image Downloader: Започва последователно изтегляне на " . count($image_urls) . " изображения с 3-секундно забавяне между всяко изтегляне.");
        }

        foreach ($image_urls as $url) {
            // Пропускаме невалидни URL-и
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                if (isset($this->log)) $this->log->write("Image Downloader: Пропуснат невалиден URL: " . $url);
                continue;
            }

            $local_relative_path = $this->_generateUniqueLocalPath($url);
            
            // Проверяваме дали файлът вече съществува локално
            if (is_file($this->getMainDownloadPath() . $local_relative_path)) {
                $downloaded_map[$url] = 'catalog/'.$local_relative_path;
                if (isset($this->log)) $this->log->write("Image Downloader: Файлът вече съществува локално: " . $local_relative_path);
                continue;
            }

            // Добавяме забавяне от 3 секунди преди всяко изтегляне (освен първото)
            if ($download_count > 0) {
                if (isset($this->log)) $this->log->write("Image Downloader: Изчакване 3 секунди преди следващото изтегляне...");
                sleep(3);
            }

            // Изтегляне на изображението с обикновен cURL
            $success = $this->_downloadSingleImage($url, $local_relative_path);
            
            if ($success) {
                $downloaded_map[$url] = 'catalog/'.$local_relative_path;
                if (isset($this->log)) $this->log->write("Image Downloader: Успешно изтеглено: " . $url . " -> " . $local_relative_path);
            } else {
                if (isset($this->log)) $this->log->write("Image Downloader: Неуспешно изтегляне на: " . $url);
            }

            $download_count++;
        }

        if (isset($this->log)) {
            $this->log->write("Image Downloader: Завършено изтегляне. Успешно изтеглени: " . count($downloaded_map) . " от " . count($image_urls) . " изображения.");
        }

        return $downloaded_map;
    }

    /**
     * Изтегля едно изображение с обикновен cURL.
     *
     * @param string $url URL на изображението
     * @param string $local_relative_path Относителен път за запис на файла
     * @return bool True при успех, false при неуспех
     */
    private function _downloadSingleImage($url, $local_relative_path) {
        // Създаване и конфигуриране на cURL handle
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);     // Връща резултата като стринг
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);     // Следва пренасочвания
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);             // Ограничение на пренасочванията
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);       // Timeout за свързване
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);              // Общ timeout за изпълнение
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);    // Често се налага за работа с различни SSL сертификати
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'); // Добавяме User-Agent за по-добра съвместимост

        // Изпълнение на заявката
        $content = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);

        // Проверка за грешки
        if ($content === false || !empty($error)) {
            if (isset($this->log)) $this->log->write("Image Downloader: cURL грешка за " . $url . ": " . $error);
            return false;
        }

        if ($http_code != 200) {
            if (isset($this->log)) $this->log->write("Image Downloader: HTTP грешка " . $http_code . " за " . $url);
            return false;
        }

        // Проверка дали съдържанието е валидно изображение (основна проверка)
        if (strlen($content) < 100) { // Твърде малко съдържание за изображение
            if (isset($this->log)) $this->log->write("Image Downloader: Твърде малко съдържание за " . $url . " (" . strlen($content) . " bytes)");
            return false;
        }

        // Запис на файла
        $full_path = $this->getMainDownloadPath() . $local_relative_path;
        
        if (file_put_contents($full_path, $content)) {
            return true;
        } else {
            if (isset($this->log)) $this->log->write("Image Downloader: Неуспешен запис на файл: " . $full_path);
            return false;
        }
    }

    /**
     * Генерира уникален локален път за даден URL.
     * @param string $url
     * @return string
     */
    private function _generateUniqueLocalPath($url) {
        $path_info = pathinfo(parse_url($url, PHP_URL_PATH));
        $extension = isset($path_info['extension']) ? strtolower($path_info['extension']) : 'jpg';

        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($extension, $allowed_extensions)) {
            return false;
        }

        $filename = basename($url);

        return $this->downloadSubdir . $filename;
    }

    /**
     * Проверява дали целевата директория съществува и я създава, ако е необходимо.
     */
    private function _ensureDirectoryExists() {
        $full_path = $this->getMainDownloadPath() . $this->downloadSubdir;
        if (!is_dir($full_path)) {
            mkdir($full_path, 0755, true);
        }
    }
}