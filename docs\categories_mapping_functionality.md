# Функционалност за съответствия на категории в Multi Feed Syncer

## Описание

Тази функционалност разширява модула Multi Feed Syncer с възможност за управление на съответствията между категориите от синхронизиращите файлове и категориите на сайта.

## Нови компоненти

### 1. Таблица в базата данни

**Таблица:** `multi_feed_syncer_categories_mapping`

**Полета:**
- `mapping_id` - Уникален идентификатор
- `mfsc_id` - ID на конектора
- `source_category_path` - Път на категорията от конектора
- `target_category_path` - Път на категорията от сайта
- `date_added` - Дата на създаване
- `date_modified` - Дата на последна модификация

### 2. Нови методи в модела

**В `admin/model/extension/module/multi_feed_syncer.php`:**
- `getCategoriesMapping($mfsc_id)` - Получава съответствията за конектор
- `saveCategoryMapping($mfsc_id, $source_path, $target_path)` - Запазва съответствие
- `searchSiteCategories($search_term, $limit)` - Търси категории за автозавършване
- `getCategoryPath($category_id)` - Получава пълния път на категория

### 3. Нови методи в конекторите

**В `admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`:**
- `getCategoriesFromFile()` - Извлича категории от синхронизиращия файл
- `getLastFileDownloadInfo()` - Информация за последното изтегляне
- `downloadFileAgain()` - Изтегля отново файла

### 4. Нови AJAX методи в контролера

**В `admin/controller/extension/module/multi_feed_syncer.php`:**
- `getLastFileDownloadInfo()` - AJAX за информация за файла
- `downloadFileAgain()` - AJAX за повторно изтегляне
- `getCategoriesMapping()` - AJAX за получаване на съответствията
- `saveCategoriesMapping()` - AJAX за запазване на съответствията
- `searchCategories()` - AJAX за търсене на категории
- `startProductsUpdate()` - AJAX за стартиране на синхронизация

## Как работи

### 1. Извличане на категории от файла

Когато се натисне бутона "Обновяване на продуктите", системата:
1. Сканира всички продукти в синхронизиращия файл
2. Извлича пътищата на категориите от полето `CategoryBranch`
3. Преобразува формата от "Категория1|Категория2|Категория3" в "Категория1 > Категория2 > Категория3"
4. Създава дърво от всички уникални категории

### 2. Управление на съответствията

1. **Автоматично генериране:** За нови категории се създават записи в таблицата
2. **Ръчно редактиране:** Потребителят може да редактира съответствията
3. **Автозавършване:** При търсене се показват предложения от категориите на сайта
4. **Запазване:** Съответствията се запазват в базата данни

### 3. Визуализация

- **Светложълт фон:** Нови или променени категории
- **Таблица:** Показва съответствията между категориите
- **Търсене:** Поле с автозавършване за избор на категории от сайта

## Технически детайли

### Формат на категориите в XML файла

```xml
<CategoryBranch>
    <BG>Хартия|Формуляри|Безопасност, хигиена и противопожарна охрана</BG>
</CategoryBranch>
```

### Формат в базата данни

```
source_category_path: "Хартия > Формуляри > Безопасност, хигиена и противопожарна охрана"
target_category_path: "Офис материали > Хартия > Формуляри"
```

### JavaScript функционалност

- **AJAX заявки:** За всички взаимодействия
- **Автозавършване:** Търсене на категории с до 10 предложения
- **Валидация:** Проверка на данните преди запазване
- **Обратна връзка:** Съобщения за успех/грешка

## Инсталация

1. Таблицата се създава автоматично при инсталация на модула
2. Методите `install()` и `uninstall()` са актуализирани
3. Езиковите променливи са добавени в `admin/language/en-gb/extension/module/multi_feed_syncer.php`

## Използване

1. Отидете в админ панела -> Разширения -> Модули -> Multi Feed Syncer
2. Натиснете бутона "Обновяване на продуктите" за желания конектор
3. Ще се покаже разширеното съдържание с:
   - Информация за последното изтегляне на файла
   - Таблица със съответствията на категориите
   - Бутон за стартиране на синхронизацията
4. Редактирайте съответствията при нужда
5. Запазете промените
6. Стартирайте обновяването на продуктите
