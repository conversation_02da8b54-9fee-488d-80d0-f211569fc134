<?php

// Скрипт, който се извиква от cron задача
// Примерна cron команда: /usr/bin/php /път/до/вашия/opencart/system/storage/cron/download_images.php >> /път/до/вашия/opencart/system/storage/logs/download_images.log 2>&1 [cite: 25, 26]

// Задаване на по-дълго време за изпълнение
set_time_limit(0);
ini_set('memory_limit', '2048M'); // Коригирайте при нужда

require_once(__DIR__ . '/init.php');

// --- Проверка дали скриптът е извикан от CLI ---
if (php_sapi_name() !== 'cli') {
    die("Този скрипт може да бъде стартиран само от командния ред (CLI).");
}

// --- Извикване на метода на контролера ---
// Зареждане на контролера
// Подаваме $registry, за да може контролерът да има достъп до всички Opencart обекти
try {
    $registry->get('load')->controller('extension/module/multi_feed_syncer/processImageDownloadQueue'); // Директно извикване
} catch (\Exception $e) {
    echo "[" . date("Y-m-d H:i:s") . "] Exception: " . $e->getMessage() . "\n";
}
?>