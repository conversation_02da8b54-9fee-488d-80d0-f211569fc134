<?php
/**
 * Пълен тест на функционалността за мапиране на категории със специални символи
 * Тества цялостния процес от запазване до извличане и сравнение
 */

// Включваме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Зареждаме config
$config = new Config();
$registry->set('config', $config);

// Зареждаме модела
$loader = new Loader($registry);
$registry->set('load', $loader);

// Зареждаме Multi Feed Syncer модела
$loader->model('extension/module/multi_feed_syncer');
$model = $registry->get('model_extension_module_multi_feed_syncer');

// Тестови данни
$test_mfsc_id = 999;
$test_cases = [
    [
        'source' => 'Монитори > 24" LED монитори > Samsung',
        'target' => 'TV, Монитори, Видео и Аудио'
    ],
    [
        'source' => 'Телевизори > 55" Smart TV > LG',
        'target' => 'TV, Монитори, Видео и Аудио'
    ],
    [
        'source' => 'Принтери > A4 "All-in-One" устройства',
        'target' => 'Принтери и Скенери'
    ],
    [
        'source' => 'Компютри > 15.6" лаптопи > Dell',
        'target' => 'Лаптопи и Нетбуци'
    ],
    [
        'source' => 'Категория с \'единични\' кавички',
        'target' => 'Тестова категория'
    ],
    [
        'source' => 'Категория с <тагове> и &амперсанд',
        'target' => 'Друга тестова категория'
    ]
];

echo "<h1>Пълен тест на мапиране на категории със специални символи</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ccc; }
</style>\n";

// Изчистваме стари тестови данни
$db->query("DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");

echo "<div class='test-section'>\n";
echo "<h2>Тест 1: Запазване на категории</h2>\n";
echo "<table>\n";
echo "<tr><th>Source категория</th><th>Target категория</th><th>Mapping ID</th><th>Статус</th></tr>\n";

$saved_mappings = [];
foreach ($test_cases as $index => $test_case) {
    try {
        $mapping_id = $model->saveCategoryMapping($test_mfsc_id, $test_case['source'], $test_case['target']);
        $saved_mappings[$index] = $mapping_id;
        
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
        echo "<td>" . htmlspecialchars($test_case['target']) . "</td>\n";
        echo "<td>{$mapping_id}</td>\n";
        echo "<td class='success'>УСПЕХ</td>\n";
        echo "</tr>\n";
    } catch (Exception $e) {
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
        echo "<td>" . htmlspecialchars($test_case['target']) . "</td>\n";
        echo "<td>-</td>\n";
        echo "<td class='error'>ГРЕШКА: " . htmlspecialchars($e->getMessage()) . "</td>\n";
        echo "</tr>\n";
    }
}
echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 2: Извличане на категории</h2>\n";

try {
    $retrieved_mappings = $model->getCategoriesMapping($test_mfsc_id);
    
    echo "<table>\n";
    echo "<tr><th>Оригинал Source</th><th>Извлечен Source</th><th>Съвпадение</th><th>Кавички запазени</th><th>Статус</th></tr>\n";
    
    foreach ($test_cases as $index => $test_case) {
        $found = false;
        foreach ($retrieved_mappings as $mapping) {
            if ($mapping['source_category_path'] === $test_case['source']) {
                $found = true;
                $quotes_preserved = strpos($mapping['source_category_path'], '"') !== false;
                $original_has_quotes = strpos($test_case['source'], '"') !== false;
                
                $status_class = 'success';
                $status_text = 'УСПЕХ';
                
                if ($original_has_quotes && !$quotes_preserved) {
                    $status_class = 'error';
                    $status_text = 'ГРЕШКА - Кавички загубени';
                }
                
                echo "<tr>\n";
                echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
                echo "<td>" . htmlspecialchars($mapping['source_category_path']) . "</td>\n";
                echo "<td>" . ($mapping['source_category_path'] === $test_case['source'] ? 'ДА' : 'НЕ') . "</td>\n";
                echo "<td>" . ($quotes_preserved ? 'ДА' : ($original_has_quotes ? 'НЕ' : 'Н/П')) . "</td>\n";
                echo "<td class='{$status_class}'>{$status_text}</td>\n";
                echo "</tr>\n";
                break;
            }
        }
        
        if (!$found) {
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
            echo "<td>НЕ НАМЕРЕНА</td>\n";
            echo "<td>НЕ</td>\n";
            echo "<td>НЕ</td>\n";
            echo "<td class='error'>ГРЕШКА - Не е намерена</td>\n";
            echo "</tr>\n";
        }
    }
    echo "</table>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>ГРЕШКА при извличане: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 3: Актуализиране на категории</h2>\n";

echo "<table>\n";
echo "<tr><th>Source категория</th><th>Нов Target</th><th>Действие</th><th>Статус</th></tr>\n";

foreach ($test_cases as $index => $test_case) {
    $new_target = $test_case['target'] . ' (Актуализирана)';
    
    try {
        $result = $model->saveOrUpdateCategoryMapping($test_mfsc_id, $test_case['source'], $new_target);
        
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
        echo "<td>" . htmlspecialchars($new_target) . "</td>\n";
        echo "<td>" . strtoupper($result['action']) . "</td>\n";
        echo "<td class='success'>УСПЕХ</td>\n";
        echo "</tr>\n";
    } catch (Exception $e) {
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['source']) . "</td>\n";
        echo "<td>" . htmlspecialchars($new_target) . "</td>\n";
        echo "<td>-</td>\n";
        echo "<td class='error'>ГРЕШКА: " . htmlspecialchars($e->getMessage()) . "</td>\n";
        echo "</tr>\n";
    }
}
echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Тест 4: Проверка на специални символи в базата данни</h2>\n";

$query = $db->query("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");

echo "<table>\n";
echo "<tr><th>Source в базата</th><th>Target в базата</th><th>Кавички в Source</th><th>Други символи</th></tr>\n";

foreach ($query->rows as $row) {
    $has_quotes = strpos($row['source_category_path'], '"') !== false;
    $has_single_quotes = strpos($row['source_category_path'], "'") !== false;
    $has_ampersand = strpos($row['source_category_path'], '&') !== false;
    $has_gt = strpos($row['source_category_path'], '>') !== false;
    $has_lt = strpos($row['source_category_path'], '<') !== false;
    
    $other_symbols = [];
    if ($has_single_quotes) $other_symbols[] = "'";
    if ($has_ampersand) $other_symbols[] = "&";
    if ($has_gt) $other_symbols[] = ">";
    if ($has_lt) $other_symbols[] = "<";
    
    echo "<tr>\n";
    echo "<td>" . htmlspecialchars($row['source_category_path']) . "</td>\n";
    echo "<td>" . htmlspecialchars($row['target_category_path']) . "</td>\n";
    echo "<td>" . ($has_quotes ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td>" . (empty($other_symbols) ? 'Няма' : implode(', ', $other_symbols)) . "</td>\n";
    echo "</tr>\n";
}
echo "</table>\n";
echo "</div>\n";

// Изчистваме тестовите данни
$db->query("DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");

echo "<div class='test-section'>\n";
echo "<h2>Заключение</h2>\n";
echo "<p>Този тест проверява цялостната функционалност за работа със специални символи в категориите:</p>\n";
echo "<ul>\n";
echo "<li><strong>Запазване:</strong> Дали специалните символи се записват правилно в базата данни</li>\n";
echo "<li><strong>Извличане:</strong> Дали се четат правилно от базата данни</li>\n";
echo "<li><strong>Актуализиране:</strong> Дали се актуализират без загуба на символи</li>\n";
echo "<li><strong>Съхранение:</strong> Дали се съхраняват правилно в базата данни</li>\n";
echo "</ul>\n";
echo "<p>Ако всички тестове показват 'УСПЕХ', корекцията работи правилно.</p>\n";
echo "</div>\n";

?>
