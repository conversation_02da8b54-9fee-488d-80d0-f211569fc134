{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {# <button type="submit" form="form-module" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button> #}
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <ul class="nav nav-tabs">
          <li class="active"><a href="#tab-suppliers" data-toggle="tab">{{ tab_suppliers }}</a></li> {# [cite: 6] #}
          <li><a href="#tab-logs" data-toggle="tab">{{ tab_logs }}</a></li> {# [cite: 6] #}
          {# Условно добавяне на таб "Dev логове" #}
          {% if is_developer %}
          <li><a href="#tab-dev-logs" data-toggle="tab">{{ tab_dev_logs }}</a></li>
          {% endif %}
        </ul>
        <div class="tab-content">
          {# Таб "Доставчици" #}
          <div class="tab-pane active" id="tab-suppliers"> {# [cite: 7] #}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td class="text-left">{{ column_supplier }}</td> {# [cite: 8] #}
                    <td class="text-left">{{ column_connection_type }}</td> {# [cite: 8] #}
                    <td class="text-center" style="width: 15%;">{{ column_markup_percentage }}</td>
                    <td class="text-center">{{ column_status }}</td> {# [cite: 8] #}
                    <td class="text-right">{{ column_action }}</td> {# [cite: 8] #}
                  </tr>
                </thead>
                <tbody id="activated-suppliers-list">
                  {% if activated_suppliers %}
                  {% for supplier in activated_suppliers %}
                  <tr id="supplier-row-{{ supplier.mfsc_id }}">
                    <td class="text-left">{{ supplier.name }}</td>
                    <td class="text-left">{{ supplier.connection_type }}</td>
                    <td class="text-center">
                        <div style="display: flex; align-items: center; justify-content: center; width: 100%;">
                          <div class="input-group" style="max-width: 60px;">
                              <input type="text" name="markup_percentage_{{ supplier.mfsc_id }}" 
                                    value="{{ supplier.markup_percentage|number_format(2, '.', '') }}" 
                                    placeholder="{{ placeholder_markup }}" class="form-control input-sm markup-percentage-input" 
                                    data-mfsc-id="{{ supplier.mfsc_id }}" style="text-align: right;"/>
                          </div>
                        </div>
                        <small class="markup-save-status text-muted" data-mfsc-id="{{ supplier.mfsc_id }}" style="display: none; margin-top: 3px; min-height: 15px;"></small>
                    </td>
                    <td class="text-center" id="status-{{ supplier.mfsc_id }}"><span class="label label-default" title="{{ text_status_unknown }}">?</span></td>
                    <td class="text-right">
                      <button type="button" class="btn btn-info btn-xs check-connection" data-connector-id="{{ supplier.mfsc_id }}" data-toggle="tooltip" title="{{ button_check_connection }}"><i class="fa fa-plug"></i></button> {# [cite: 8] #}
                      <button type="button" class="btn btn-primary btn-xs button-copy-cron-js" 
                            data-cron-info="{{ supplier.cron_info }}" 
                            data-original-text="{{ button_copy_cron_text|escape('html_attr') }}"
                            data-toggle="tooltip" title="{{ tooltip_copy_cron|escape('html_attr') }}">
                            <i class="fa fa-copy"></i> <span class="copy-text">{{ button_copy_cron_text }}</span>
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td class="text-center" colspan="4">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                </tbody>
              </table>

              {% if cron_status_message is defined %}
              <div class="panel panel-default">
                <div class="panel-heading">
                  <h3 class="panel-title"><i class="fa fa-clock-o"></i> {{ text_cron_setup_notice_title }}</h3>
                </div>
                <div class="panel-body">
                  <p class="{% if cron_is_active %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cron_status_message }}</strong>
                  </p>
                  {% if not cron_is_active and cron_command_suggestion is defined %}
                    <p>{{ text_cron_command_suggestion }}</p>
                    <div class="form-group">
                      <div class="input-group">
                        <input type="text" class="form-control" value="{{ cron_command_suggestion|escape('html_attr') }}" readonly id="cron-command-to-copy">
                        <span class="input-group-btn">
                          <button class="btn btn-default" type="button" id="button-copy-suggested-cron" title="Копирай командата"><i class="fa fa-copy"></i></button>
                        </span>
                      </div>
                      <span id="cron-copied-feedback" style="color: green; display: none; margin-left: 10px;">Копирано!</span>
                    </div>
                  {% endif %}
                  {% if cron_details is defined and cron_details %}
                    <h5>{{ text_cron_status_details }}</h5>
                    <ul>
                    {% for detail in cron_details %}
                      <li>{{ detail }}</li>
                    {% endfor %}
                    </ul>
                  {% endif %}
                </div>
              </div>
              {% endif %}
            </div>
            
            {% if new_connectors %}
            <h4>{{ text_new_connectors }}</h4> {# [cite: 13] #}
            <div class="table-responsive">
              <table class="table table-bordered">
                <tbody id="new-connectors-list">
                  {% for new_connector in new_connectors %}
                  <tr id="new-connector-row-{{ new_connector.connector_key }}"> {# [cite: 18] #}
                    <td class="text-left">{{ new_connector.name }}</td> {# [cite: 14] #}
                    <td class="text-right">
                      <button type="button" class="btn btn-success btn-sm activate-connector" data-connector-key="{{ new_connector.connector_key }}"><i class="fa fa-plus-circle"></i> {{ text_activate }}</button> {# [cite: 14, 16] #}
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% endif %}
          </div>

          {# Таб "Логове" #}
          <div class="tab-pane" id="tab-logs"> {# [cite: 19] #}
            <h3 style="font-size: 18px; font-weight: bold;">{{ text_logs_title }}</h3> {# [cite: 20] #}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr> {# [cite: 22] #}
                    <td class="text-left">{{ column_date }}</td> 
                    <td class="text-left">{{ column_log_supplier }}</td> 
                    <td class="text-center">{{ column_connection_status }}</td> 
                    <td class="text-right">{{ column_duration }}</td> 
                    <td class="text-left">{{ column_information }}</td> 
                  </tr>
                </thead>
                <tbody>
                  {% if logs %}
                  {% for log in logs %}
                  <tr>
                    <td class="text-left">{{ log.date }}</td>
                    <td class="text-left">{{ log.supplier }}</td>
                    <td class="text-center">
                        {% if log.connection_status == text_success_status %}
                            <span class="label label-success">✓</span> {# [cite: 9] #}
                        {% else %}
                             <span class="label label-danger">✕</span> {# [cite: 9] #}
                        {% endif %}
                    </td>
                    <td class="text-right">{{ log.duration }}</td>
                    <td class="text-left">{{ log.information }}</td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td class="text-center" colspan="5">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                </tbody>
              </table>
            </div>
            <div class="row"> {# [cite: 21] #}
              <div class="col-sm-6 text-left">{{ pagination }}</div>
              <div class="col-sm-6 text-right">{{ results_count }}</div>
            </div>
          </div>

            <input type="hidden" id="is_developer" value="{{ is_developer }}">

          {# Условно добавяне на съдържанието на таб "Dev логове" #}
          {% if is_developer %}
          
          <div class="tab-pane" id="tab-dev-logs">
            <div class="pull-right"> {# Подравняване на бутона вдясно #}
                <button type="button" id="button-clear-dev-log" class="btn btn-danger btn-sm" data-toggle="tooltip" title="{{ button_clear_dev_log }}"><i class="fa fa-trash"></i> {{ button_clear_dev_log }}</button>
            </div>
            <h3 style="font-size: 18px; font-weight: bold;">{{ tab_dev_logs }}</h3>
            <div id="dev-log-content-area"> {# Добавяме ID на div-а, за да можем лесно да го изчистим #}
                {% if dev_log_content and dev_log_content|trim is not empty %}
                    <pre style="max-height: 600px; overflow-y: auto; white-space: pre-wrap; word-wrap: break-word; background-color: #f5f5f5; border: 1px solid #ccc; padding: 10px; border-radius: 4px;">{{ dev_log_content }}</pre>
                {% else %}
                    <p>{{ text_no_results }}</p> {# Показва "Няма резултати", ако dev_log_content е празен или съдържа само празни редове #}
                {% endif %}
            </div>
          </div>
          {% endif %}

        </div>
      </div>
    </div>
  </div>
<script type="text/javascript">
$(document).ready(function() {
    // Проверка на статуса на връзката за всички изброени доставчици при зареждане на страницата
    $('.check-connection').each(function() {
        var button = $(this);
        var connector_id = button.data('connector-id');
        var status_cell = $('#status-' + connector_id);
        status_cell.html('<i class="fa fa-spinner fa-spin"></i>');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/checkConnection&user_token={{ user_token }}&connector_id=' + connector_id,
            dataType: 'json',
            success: function(json) {
                if (json['status'] == 1) {
                    status_cell.html('<span class="label label-success" data-toggle="tooltip" title="' + json['message'] + '">✓</span>');
                } else {
                    status_cell.html('<span class="label label-danger" data-toggle="tooltip" title="' + json['message'] + '">✕</span>');
                }
                $('[data-toggle="tooltip"]').tooltip(); // Реинициализация на tooltip-овете
            },
            error: function(xhr, ajaxOptions, thrownError) {
                status_cell.html('<span class="label label-danger" data-toggle="tooltip" title="AJAX Грешка">✕</span>');
                console.log('AJAX Грешка: ' + thrownError + "\n" + xhr.responseText);
                $('[data-toggle="tooltip"]').tooltip();
            }
        });
    });

    // AJAX за бутона "Провери връзката"
    $(document).on('click', '.check-connection', function() {
        var button = $(this);
        var connector_id = button.data('connector-id');
        var status_cell = $('#status-' + connector_id);
        var original_html = status_cell.html();
        status_cell.html('<i class="fa fa-spinner fa-spin"></i>'); // Индикатор за зареждане
        button.prop('disabled', true);

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/checkConnection&user_token={{ user_token }}&connector_id=' + connector_id,
            dataType: 'json',
            success: function(json) {
                if (json['status'] == 1) {
                    status_cell.html('<span class="label label-success" data-toggle="tooltip" title="' + json['message'] + '">✓</span>');
                } else {
                    status_cell.html('<span class="label label-danger" data-toggle="tooltip" title="' + json['message'] + '">✕</span>');
                }
                 $('[data-toggle="tooltip"]').tooltip();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                status_cell.html(original_html); // Връщане на оригиналния HTML при грешка
                alert('AJAX Грешка: ' + thrownError + "\n" + xhr.responseText);
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Копиране на Cron командата
    $(document).on('click', '.button-copy-cron-js', function(event) {
        event.preventDefault();
        var $button = $(this);
        var cronInfo = $button.data('cron-info');
        // Запазваме оригиналния текст от data атрибут, за да е устойчиво на многоезичност
        var originalText = $button.data('original-text') ? $button.data('original-text') : '{{ button_copy_cron_text|escape('js') }}'; 
        var $copyTextSpan = $button.find('.copy-text');
        var $icon = $button.find('i');
        var originalIconClass = $icon.attr('class'); // Запазваме оригиналния клас на иконата
        var success = false;

        // Създаване на временен input елемент
        var $tempInput = $('<input>');
        $('body').append($tempInput);
        $tempInput.val(cronInfo).select(); // Слагаме стойността и я избираме

        // Опит за копиране чрез document.execCommand
        try {
            success = document.execCommand('copy');
        } catch (err) {
            console.error("Грешка при document.execCommand('copy') за cron: ", err);
            success = false;
        }

        $tempInput.remove(); // Премахване на временния елемент

        if (success) {
            $button.removeClass('btn-primary btn-danger').addClass('btn-success'); // Променяме класа на бутона за успех
            $copyTextSpan.text('{{ text_copied|escape('js') }}'); // Използваме езиковата променлива "Копирано"
            $icon.attr('class', 'fa fa-check'); // Променяме иконата

            setTimeout(function() {
                $button.removeClass('btn-success').addClass('btn-primary'); // Връщаме оригиналния клас
                $copyTextSpan.text(originalText); // Връщаме оригиналния текст на бутона
                $icon.attr('class', originalIconClass); // Връщаме оригиналната икона
            }, 2000); // Време за показване на "Копирано!" (2 секунди)
        } else {
            $button.removeClass('btn-primary btn-success').addClass('btn-danger'); // Клас за грешка
            $copyTextSpan.text('{{ text_cron_copy_error|escape('js') }}'); // Използваме езиковата променлива за грешка
            $icon.attr('class', 'fa fa-times'); // Икона за грешка
            console.error('Копирането на Cron командата е неуспешно.');

            setTimeout(function() {
                $button.removeClass('btn-danger').addClass('btn-primary');
                $copyTextSpan.text(originalText);
                $icon.attr('class', originalIconClass);
            }, 2500); // Време за показване на съобщението за грешка
        }
    });

    // Активиране на конектор
    $(document).on('click', '.activate-connector', function() {
        var button = $(this);
        var connector_key = button.data('connector-key');
        button.prop('disabled', true).prepend('<i class="fa fa-spinner fa-spin"></i> ');

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/activateConnector&user_token={{ user_token }}',
            type: 'POST',
            dataType: 'json',
            data: { connector_key: connector_key },
            success: function(json) {
                if (json['success']) {
                    // Динамично добавяне към списъка с активни конектори
                    var new_row_html = '<tr id="supplier-row-' + json.new_connector_data.mfsc_id + '">' +
                    '<td class="text-left">' + json.new_connector_data.name + '</td>' +
                    '<td class="text-left">' + json.new_connector_data.connection_type + '</td>' +
                    '<td class="text-center" id="status-' + json.new_connector_data.mfsc_id + '"><span class="label label-default" title="' + '{{ text_status_unknown|escape('js') }}' +'">?</span></td>' +
                    '<td class="text-right">' +
                    '  <button type="button" class="btn btn-info btn-xs check-connection" data-connector-id="' + json.new_connector_data.mfsc_id + '" data-toggle="tooltip" title="' + '{{ button_check_connection|escape('js') }}' + '"><i class="fa fa-plug"></i></button>' +
                    // Коригиран HTML за бутона "Копирай cron"
                    '  <button type="button" class="btn btn-primary btn-xs button-copy-cron-js" ' +
                    '          data-cron-info="' + json.new_connector_data.cron_info + '" ' +
                    '          data-original-text="' + '{{ button_copy_cron_text|escape('js') }}' + '" ' + // Използваме новия текст за бутона
                    '          data-toggle="tooltip" title="' + '{{ tooltip_copy_cron|escape('js') }}' + '">' + // Използваме новия tooltip
                    '      <i class="fa fa-copy"></i> <span class="copy-text">' + '{{ button_copy_cron_text|escape('js') }}' + '</span>' + // Текст на бутона
                    '  </button>' +
                    // Старият span за "копирано" до бутона вече не е нужен тук, тъй като обратната връзка е на самия бутон
                    '</td></tr>';

                    if ($('#activated-suppliers-list').find('td[colspan="4"]').length > 0) { // Ако има "Няма резултати"
                        $('#activated-suppliers-list').html(new_row_html);
                    } else {
                        $('#activated-suppliers-list').append(new_row_html);
                    }

                    $('#new-connector-row-' + connector_key).remove(); // Премахване от списъка с нови

                    // Задействане на проверка на връзката за новия ред
                    $('#supplier-row-' + json.new_connector_data.mfsc_id).find('.check-connection').trigger('click');
                    $('[data-toggle="tooltip"]').tooltip(); // Реинициализация


                    // Ако списъкът с нови конектори стане празен, скрий го
                    if ($('#new-connectors-list tr').length == 0) {
                        $('#new-connectors-list').closest('.table-responsive').prev('h4').remove();
                        $('#new-connectors-list').closest('.table-responsive').remove();
                    }

                    // Показване на съобщение за успех
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();
                    $('#content .container-fluid:first').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');

                }
                if (json['error']) {
                    // Показване на съобщение за грешка
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();
                    $('#content .container-fluid:first').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    button.prop('disabled', false).find('.fa-spinner').remove();
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert('AJAX Грешка: ' + thrownError + "\n" + xhr.responseText);
                button.prop('disabled', false).find('.fa-spinner').remove();
            }
        });
    });

    $('[data-toggle="tooltip"]').tooltip(); // Инициализация на tooltip-овете

    // Изчистване на Dev лога
    $(document).on('click', '#button-clear-dev-log', function() {
        // Използваме езиковата променлива за потвърждение
        if (confirm('{{ text_confirm_clear_dev_log }}')) {
            var $button = $(this);
            var original_button_html = $button.html(); // Запазваме оригиналния HTML на бутона
            $button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_processing|escape('js') }}'); // Използваме езикова променлива за "Обработка"

            $.ajax({
                url: 'index.php?route=extension/module/multi_feed_syncer/clearDevLog&user_token={{ user_token }}',
                type: 'POST', // POST е по-подходящ за действия, които променят състоянието на сървъра
                dataType: 'json',
                success: function(json) {
                    // Премахване на предишни съобщения за успех/грешка
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();

                    if (json['success']) {
                        // Изчистване на показаното съдържание на лога
                        $('#dev-log-content-area').html('<p>{{ text_no_results|escape('js') }}</p>'); 
                        // Показване на съобщение за успех
                        $('#content .container-fluid:first').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    }
                    if (json['error']) {
                        // Показване на съобщение за грешка
                        $('#content .container-fluid:first').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    // Премахване на предишни съобщения за успех/грешка
                    $('#content .container-fluid:first > .alert-success, #content .container-fluid:first > .alert-danger').remove();
                    // Показване на AJAX грешка
                    $('#content .container-fluid:first').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + 'AJAX Грешка: ' + thrownError + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    console.log('AJAX Грешка при изчистване на Dev лога: ' + thrownError + "\n" + xhr.responseText);
                },
                complete: function() {
                    // Връщане на оригиналния вид на бутона
                    $button.prop('disabled', false).html(original_button_html);
                }
            });
        }
    });

    // Запазване на Процент Надценка при промяна (on change)
    $(document).on('change', '.markup-percentage-input', function() {
        var $input = $(this);
        var mfsc_id = $input.data('mfsc-id');
        var markup_value = $input.val().replace(',', '.');
        var $status_span = $('.markup-save-status[data-mfsc-id="' + mfsc_id + '"]');

        if (markup_value.trim() === '' || isNaN(parseFloat(markup_value))) {
            $status_span.html('<span class="text-danger">{{ text_markup_invalid_value|escape('js') }}</span>').fadeIn();
            setTimeout(function() { $status_span.fadeOut().html(''); }, 3000);
            $input.val($input.data('original-value'));
            return;
        }

        $status_span.html('<span class="text-info"><i class="fa fa-spinner fa-spin"></i> {{ text_processing|escape('js') }}</span>').fadeIn();

        $.ajax({
            url: 'index.php?route=extension/module/multi_feed_syncer/saveMarkup&user_token={{ user_token }}',
            type: 'POST',
            data: {
                mfsc_id: mfsc_id,
                markup: markup_value
            },
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $status_span.html('<span class="text-success">' + json['success'] + '</span>').fadeIn();
                    $input.val(parseFloat(markup_value).toFixed(2));
                    $input.data('original-value', parseFloat(markup_value).toFixed(2));
                }
                if (json['error']) {
                    $status_span.html('<span class="text-danger">' + json['error'] + '</span>').fadeIn();
                    $input.val($input.data('original-value'));
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $status_span.html('<span class="text-danger">' + 'AJAX Грешка: ' + thrownError + '</span>').fadeIn();
                $input.val($input.data('original-value'));
                console.log('AJAX Грешка при запазване на надценка: ' + thrownError + "\n" + xhr.responseText);
            },
            complete: function() {
                if (!$status_span.find('.text-danger').length) {
                    setTimeout(function() { $status_span.fadeOut().html(''); }, 3000);
                }
            }
        });
    });

    $(document).on('click', '#button-copy-suggested-cron', function() {
      var cronCommandInput = document.getElementById('cron-command-to-copy');
      var success = false;

      // Избиране на текста
      cronCommandInput.select();
      if (typeof cronCommandInput.setSelectionRange === "function") { 
          cronCommandInput.setSelectionRange(0, 99999); // За мобилни
      } 

      try {
          success = document.execCommand('copy');
      } catch (err) {
          success = false;
          console.error("Грешка при копиране на cron командата: ", err);
      }

      if (success) {
          $('#cron-copied-feedback').fadeIn().delay(2000).fadeOut();
      } else {
          alert('Неуспешно копиране. Моля, копирайте ръчно.');
      }
      // Деселектиране (премахване на фокуса)
      if (window.getSelection) {
        window.getSelection().removeAllRanges();
      } else if (document.selection) {
        document.selection.empty();
      }
  });


});


</script>
</div>
{{ footer }}