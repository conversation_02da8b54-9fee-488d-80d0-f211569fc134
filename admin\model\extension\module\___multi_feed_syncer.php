<?php
class ModelExtensionModuleMultiFeedSyncer extends Model {

    public function install() {
        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_logs` (
                `mfs_id` INT AUTO_INCREMENT,
                `mfsc_id` INT,
                `connection_status` TINYINT(1),
                `process_timing` INT,
                `process_data` TEXT,
                `process_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`mfs_id`),
                INDEX `mfsc_id` (`mfsc_id`),
                INDEX `connection_status` (`connection_status`),
                INDEX `process_timing` (`process_timing`),
                INDEX `process_date` (`process_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ");

        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_connectors` (
                `mfsc_id` INT AUTO_INCREMENT,
                `connector` VARCHAR(32),
                `connector_key` VARCHAR(32) UNIQUE,
                `markup_percentage` DECIMAL(5,2) DEFAULT 0.00,
                PRIMARY KEY (`mfsc_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ");

        $query = $this->_executeQuery("SHOW COLUMNS FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` LIKE 'markup_percentage'");
        if (!$query->num_rows) {
            $this->_executeQuery("ALTER TABLE `" . DB_PREFIX . "multi_feed_syncer_connectors` ADD `markup_percentage` DECIMAL(5,2) DEFAULT 0.00 AFTER `connector_key`");
        }
    }

    public function uninstall() {
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_logs`;");
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_connectors`;");
    }

    public function getActivatedConnectors() {
        $query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` ORDER BY `connector` ASC");
        return $query->rows;
    }

    public function addConnector($data) {
        $this->_executeQuery("INSERT INTO " . DB_PREFIX . "multi_feed_syncer_connectors SET connector = '" . $this->db->escape($data['connector']) . "', connector_key = '" . $this->db->escape($data['connector_key']) . "'");
        return $this->db->getLastId();
    }
    
    public function getConnector($mfsc_id) {
        $query = $this->_executeQuery("SELECT * FROM " . DB_PREFIX . "multi_feed_syncer_connectors WHERE mfsc_id = '" . (int)$mfsc_id . "'");
        return $query->row;
    }

    /**
     * Взема данни за конектор по неговия ключ (connector_key).
     *
     * @param string $connector_key Ключът на конектора.
     * @return array|false Връща асоциативен масив с данните за конектора или false, ако не е намерен.
     */
    public function getConnectorByKey($connector_key) {
        $query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` WHERE `connector_key` = '" . $this->db->escape($connector_key) . "'");
        if ($query->num_rows) {
            return $query->row;
        } else {
            return false;
        }
    }

    public function getLogs($data = array()) {
        $sql = "SELECT mfs.*, mfc.connector FROM `" . DB_PREFIX . "multi_feed_syncer_logs` mfs LEFT JOIN `" . DB_PREFIX . "multi_feed_syncer_connectors` mfc ON (mfs.mfsc_id = mfc.mfsc_id)";
        
        $sql .= " ORDER BY mfs.process_date DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 10; // По подразбиране 10 лога на страница
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->_executeQuery($sql);
        return $query->rows;
    }

    public function getTotalLogs() {
        $query = $this->_executeQuery("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "multi_feed_syncer_logs`");
        return $query->row['total'];
    }
    
    public function addLog($log_data) {
        $this->_executeQuery("INSERT INTO " . DB_PREFIX . "multi_feed_syncer_logs SET 
            mfsc_id = '" . (int)$log_data['mfsc_id'] . "', 
            connection_status = '" . (int)$log_data['connection_status'] . "', 
            process_timing = '" . (int)$log_data['process_timing'] . "', 
            process_data = '" . $this->db->escape(isset($log_data['process_data']) ? serialize($log_data['process_data']) : '') . "', 
            process_date = NOW()");
        return $this->db->getLastId();
    }

    public function updateMarkupPercentage($mfsc_id, $markup) {
        $this->_executeQuery("UPDATE " . DB_PREFIX . "multi_feed_syncer_connectors SET markup_percentage = '" . (float)$markup . "' WHERE mfsc_id = '" . (int)$mfsc_id . "'");
    }

    /**
     * Обработва синхронизацията на продуктовите данни.
     * Този метод ще бъде описан подробно по-късно. [cite: 46]
     */
    public function setTestMode(bool $mode) {
        $this->test_mode = $mode;
        if ($this->test_mode) {
            $this->log->write("MultiFeed Syncer: ТЕСТОВ РЕЖИМ АКТИВИРАН.");
        } else {
            $this->log->write("MultiFeed Syncer: ТЕСТОВ РЕЖИМ ДЕАКТИВИРАН.");
        }
    }

    /**
     * Добавя запис в лога за синхронизациите.
     * Използва полетата от таблицата oc_multi_feed_syncer_logs.
     *
     * @param int $mfsc_id ID на конектора
     * @param array $sync_stats Асоциативен масив със статистиките от синхронизацията
     * @param int $execution_time Време за изпълнение в секунди
     */
    public function addSynchronizationLog($mfsc_id, $sync_stats, $execution_time) {
        // Определяне на connection_status.
        // За момента, ако има грешки, приемаме, че connection_status е 0 (неуспех/проблем), иначе 1 (успех).
        $connection_status_val = (isset($sync_stats['errors']) && $sync_stats['errors'] > 0) ? 0 : 1;

        $sql = "INSERT INTO `" . DB_PREFIX . "multi_feed_syncer_logs` SET ";
        $sql .= "`mfsc_id` = " . (int)$mfsc_id . ", ";
        $sql .= "`connection_status` = " . (int)$connection_status_val . ", ";
        $sql .= "`process_timing` = " . (int)$execution_time . ", ";
        // Използваме json_encode за process_data, тъй като е по-стандартно и лесно за разчитане от serialize
        $sql .= "`process_data` = '" . $this->db->escape(json_encode($sync_stats)) . "', ";
        $sql .= "`process_date` = NOW()";
        
        // Използваме _executeQuery, за да сме съвместими с тестовия режим
        $this->_executeQuery($sql);
    }

    private $logger;
    private $test_mode = false;
    private $current_dummy_product_id; // For dummy product IDs in test mode
    private $current_dummy_general_id_counter = 0; // For general dummy getLastId() in test mode

    private function _executeQuery(string $sql) {
        $trimmed_sql = trim($sql);
        $sql_command_first_word = '';
        if (strpos($trimmed_sql, ' ') !== false) {
            $sql_command_first_word = strtoupper(substr($trimmed_sql, 0, strpos($trimmed_sql, ' ')));
        } else {
            $sql_command_first_word = strtoupper($trimmed_sql); // For single-word commands if any
        }

        if ($this->test_mode) {
            if ($sql_command_first_word == 'INSERT' || $sql_command_first_word == 'UPDATE' || $sql_command_first_word == 'DELETE') {
                $this->log->write("MultiFeed Syncer (Test Mode SQL - SKIPPED EXECUTION): " . $sql);
                return true; // Simulate success
            } else if ($sql_command_first_word == 'CREATE' || $sql_command_first_word == 'ALTER' || $sql_command_first_word == 'DROP') {
                $this->log->write("MultiFeed Syncer (Test Mode SQL - SKIPPED EXECUTION): " . $sql);
                return true; // Simulate success
            } else if ($sql_command_first_word == 'SELECT' || $sql_command_first_word == 'SHOW') {
                // SELECT and SHOW queries execute normally even in test mode, but are logged.
                $this->log->write("MultiFeed Syncer (Test Mode SQL - EXECUTED): " . $sql);
                return $this->db->query($sql);
            } else {
                // For any other unhandled SQL command type in test mode
                $this->log->write("MultiFeed Syncer (Test Mode SQL - SKIPPED EXECUTION - Unhandled Type '" . $sql_command_first_word . "'): " . $sql);
                return true; // Default to skip and simulate success
            }
        }
        
        // If not in test mode, execute all queries normally
        return $this->db->query($sql);
    }

    private function _getLastId() {
        if ($this->test_mode) {
            // If INSERTs are skipped, db->getLastId() would be unreliable.
            $dummy_id = 700000 + $this->current_dummy_general_id_counter++; // Base for general last IDs
            $this->log->write("MultiFeed Syncer (Test Mode): _getLastId() called, returning dummy ID: " . $dummy_id);
            return $dummy_id;
        }
        return $this->db->getLastId();
    }

    public function doSync($opencart_product_data, $mfsc_id) {

        // $this->load->model('localisation/language');

   
        $processed_info = [
            'added' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'received' => count($opencart_product_data), // Брой получени продукти за обработка
            'log_details' => [] // За по-подробни съобщения
        ];


        if (empty($opencart_product_data)) {
            $processed_info['log_details'][] = "Няма продукти за синхронизиране.";
            return $processed_info;
        }

        // Вземане на ID на езика по подразбиране, ако не е зададен
        // Това е важно, тъй като product_description и други езиково-зависими полета го изискват.
        $default_language_id = (int)$this->registry->get('config')->get('config_language_id');

        // $languages = $this->model_localisation_language->getLanguages();

        $languages = [];

 
        // Fetch markup percentage for the current connector
        $query_markup = $this->_executeQuery("SELECT markup_percentage FROM " . DB_PREFIX . "multi_feed_syncer_connectors WHERE mfsc_id = '" . (int)$mfsc_id . "'");
        $markup_percentage = 0.0;
        if ($query_markup->num_rows && isset($query_markup->row['markup_percentage'])) {
            $markup_percentage = (float)$query_markup->row['markup_percentage'];
        } else {
            // Log if markup is not found, as it's crucial for price calculation
            $processed_info['log_details'][] = "ПРЕДУПРЕЖДЕНИЕ: Процентна надценка (markup_percentage) не е намерена за конектор ID: {$mfsc_id}. Цените може да не са коректни.";
        }

        // 'ProductCode' от XML се мапира към 'sku' в OpenCart.
        $identifier_field = 'sku'; 

        $product_codes_to_check = [];
        $valid_products_for_processing = []; // Ще съдържа продуктите с валиден идентификатор и езикови данни

 
        foreach ($opencart_product_data as $product_key => $product_data) {
            
            if (empty($product_data[$identifier_field])) {
                $processed_info['skipped']++;
                $processed_info['log_details'][] = "Продукт (#{$product_key}) е пропуснат поради липсващ идентификатор ('{$identifier_field}').";
                continue;
            }


            $sku = $product_data[$identifier_field];
            $sku_escaped = $this->db->escape($sku);

            $product_codes_to_check[] = $sku_escaped;
            // Запазваме оригиналния ключ, за да можем да достъпим пълните данни по-късно
            $valid_products_for_processing[$sku] = $product_data; 

        }

        $existing_products_map = []; // [product_code => product_id]
        if (!empty($product_codes_to_check)) {
            $query_codes_string = "'" . implode("','", $product_codes_to_check) . "'";
            $query = $this->_executeQuery("SELECT {$identifier_field}, product_id FROM " . DB_PREFIX . "product WHERE {$identifier_field} IN (" . $query_codes_string . ")");
            foreach ($query->rows as $row) {
                $existing_products_map[$row[$identifier_field]] = $row['product_id'];
            }
        }

        $products_to_add = [];
        $products_to_update = []; // [product_id => data]

        if ($this->test_mode) {
            $found_skus_log = [];
            $not_found_skus_log = [];
            foreach ($valid_products_for_processing as $product_code => $product_data_item) {
                if (isset($existing_products_map[$product_code])) {
                    $found_skus_log[] = $product_code . " (ID: " . $existing_products_map[$product_code] . ")";
                } else {
                    $not_found_skus_log[] = $product_code;
                }
            }
            $this->writeToCronLog("MultiFeed Syncer (Test Mode): Намерени съществуващи продукти (SKU -> ID):" . print_r($found_skus_log, true));
            $this->writeToCronLog("MultiFeed Syncer (Test Mode): НЕнамерени продукти (ще бъдат добавени като нови - SKU):" . print_r($not_found_skus_log, true));
        }

        foreach ($valid_products_for_processing as $product_code => $product_data) { // Този цикъл вече е изпълнен по-горе за целите на логването в тестов режим, но е нужен за реалното разделяне
            if (isset($existing_products_map[$product_code])) {
                $product_id = $existing_products_map[$product_code];
                $product_data['product_id'] = $product_id; // Добавяме product_id към данните за актуализация
                $products_to_update[$product_id] = $this->_filterProductDataForUpdate($product_data);
            } else {
                $products_to_add[$product_code] = $product_data; // Използваме product_code като ключ, докато не получим product_id
            }
        }

        if (!empty($products_to_add)) {
            $this->_processProductsToInsert($products_to_add, $processed_info, $languages, $default_language_id, $markup_percentage, $identifier_field);
        }

        if (!empty($products_to_update)) {
            $this->_processProductsToUpdate($products_to_update, $processed_info, $markup_percentage, $identifier_field);
        }

        return $processed_info;
    }


    private function _processProductsToInsert($products_to_add, &$processed_info, $languages, $default_language_id, $markup_percentage, $identifier_field) {
        $num_to_add = count($products_to_add);
        $processed_info['log_details'][] = "Подготвени {$num_to_add} продукта за пакетно добавяне.";

        try {
            $inserted_map = $this->_batchInsertProducts($products_to_add, $languages, $default_language_id, $markup_percentage, $identifier_field);
            $successfully_added_count = 0;
            foreach ($inserted_map as $sku => $new_id) {
                if ($new_id) { // Проверка дали ID-то е валидно
                    $successfully_added_count++;
                    $processed_info['log_details'][] = "Продукт '{$sku}' (New ID: {$new_id}) е добавен успешно чрез _batchInsertProducts (основна табл.).";
                } else {
                    // Ако $new_id не е валидно, това означава, че продуктът не е намерен след INSERT-а, което е проблем
                    $processed_info['errors']++;
                    $processed_info['log_details'][] = "Грешка: Продукт '{$sku}' изглежда е вмъкнат, но ID не е извлечено след _batchInsertProducts.";
                }
            }
            $processed_info['added'] += $successfully_added_count;

            // Проверка дали всички продукти, изпратени за добавяне, са получили ID
            if ($successfully_added_count < $num_to_add) {
                $failed_to_get_id_count = $num_to_add - $successfully_added_count;
                $processed_info['errors'] += $failed_to_get_id_count;
                $processed_info['log_details'][] = "{$failed_to_get_id_count} продукта не са получили валидно ID след пакетното добавяне.";
            }

        } catch (Exception $e) {
            $processed_info['errors'] += $num_to_add; // Всички се считат за грешка при обща изключение
            $processed_info['log_details'][] = "Критична грешка по време на _batchInsertProducts: " . $e->getMessage();
        }
    }

    private function _processProductsToUpdate($products_to_update, &$processed_info, $markup_percentage, $identifier_field) {
        $num_to_update = count($products_to_update);
        $processed_info['log_details'][] = "Подготвени {$num_to_update} продукта за пакетно актуализиране (цена и количество).";

        try {
            $updated_count = $this->_batchUpdateProducts($products_to_update, $markup_percentage, $identifier_field);
            $processed_info['updated'] += $updated_count;
            if ($updated_count < $num_to_update) {
                $processed_info['errors'] += ($num_to_update - $updated_count);
                $processed_info['log_details'][] = ($num_to_update - $updated_count) . " продукта не можаха да бъдат актуализирани по време на _batchUpdateProducts.";
            }
        } catch (Exception $e) {
            $processed_info['errors'] += $num_to_update;
            $processed_info['log_details'][] = "Критична грешка по време на _batchUpdateProducts: " . $e->getMessage();
        }
    }
        
    






    private function _filterProductDataForUpdate($product_data) {
        $filtered_data = [
            'product_id' => $product_data['product_id'],
            'name' => $product_data['name'],
            'price' => $product_data['price'],
            'quantity' => $product_data['quantity']
        ];
        return $filtered_data;
    }

    private function writeToCronLog($message) {
        $log_file = DIR_LOGS . 'multi_feed_syncer.log'; // [cite: 26]
        file_put_contents($log_file, $message . PHP_EOL, FILE_APPEND);
    }

    /**
     * Генерира SEO keyword от текст.
     * @param string $text
     * @return string
     */
    private function _generateSeoKeyword($text) {
        // Масив за транслитерация от кирилица към латиница
        $cyrillic = [
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ж' => 'zh', 'з' => 'z',
            'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n', 'о' => 'o', 'п' => 'p',
            'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch',
            'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya',
            'А' => 'A', 'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D', 'Е' => 'E', 'Ж' => 'Zh', 'З' => 'Z',
            'И' => 'I', 'Й' => 'Y', 'К' => 'K', 'Л' => 'L', 'М' => 'M', 'Н' => 'N', 'О' => 'O', 'П' => 'P',
            'Р' => 'R', 'С' => 'S', 'Т' => 'T', 'У' => 'U', 'Ф' => 'F', 'Х' => 'H', 'Ц' => 'Ts', 'Ч' => 'Ch',
            'Ш' => 'Sh', 'Щ' => 'Sht', 'Ъ' => 'A', 'Ь' => 'Y', 'Ю' => 'Yu', 'Я' => 'Ya', ' ' => '-',
            'ьо' => 'yo', 'ЬО' => 'Yo', 'йо' => 'yo', 'ЙО' => 'Yo', 'дж' => 'dj', 'Дж' => 'Dj', 'ДЖ' => 'DJ'
        ];

        // Премахване на HTML тагове
        $text = strip_tags(html_entity_decode($text, ENT_QUOTES, 'UTF-8'));
        
        // Транслитерация на кирилица
        $text = str_replace(array_keys($cyrillic), array_values($cyrillic), $text);
        
        // Преобразуване в малки букви
        $text = mb_strtolower($text, 'UTF-8');
        
        // Замяна на не-буквено-цифрови символи с тирета
        $text = preg_replace('~[^\pL\d]+~u', '-', $text);
        
        // Премахване на начални и крайни тирета
        $text = trim($text, '-');
        
        // Премахване на повтарящи се тирета
        $text = preg_replace('~-+~', '-', $text);

        if (empty($text)) {
            return 'n-a-' . time(); // Резервен вариант, ако текстът е празен
        }

        return $text;
    }

    /**
     * Пакетно добавяне на нови продукти.
     * @param array $products_to_add Масив от продуктови данни [sku => product_data]
     * @param array $languages Масив с езиците от OpenCart
     * @param int $default_language_id ID на езика по подразбиране
     * @return array Мапинг на [sku => new_product_id]
     */
    private function _batchInsertProducts(array $products_to_add, array $languages, int $default_language_id, float $markup_percentage, string $identifier_field) {
        $new_product_ids_map = []; // [sku => new_product_id]
        if (empty($products_to_add)) {
            return $new_product_ids_map;
        }

        $product_values = [];
        $product_skus_for_id_retrieval = [];

        // Стойности по подразбиране - може да се изнесат в конфигурация или да се вземат от $this->config
        $default_stock_status_id = (int)$this->config->get('config_stock_status_id'); 
        $default_tax_class_id = 0; 
        $default_length_class_id = (int)$this->config->get('config_length_class_id');
        $default_weight_class_id = (int)$this->config->get('config_weight_class_id');

        foreach ($products_to_add as $sku => $data) {
            $product_skus_for_id_retrieval[] = $this->db->escape($sku);

            $image = isset($data['image']) ? $this->db->escape($data['image']) : '';
            
            $model = isset($data['model']) ? $this->db->escape($data['model']) : $this->db->escape($sku);
            $quantity = isset($data['quantity']) ? (int)$data['quantity'] : 0;
            
            // Изчисляване на цена с надценка
            $feed_price = isset($data['price']) ? (float)$data['price'] : 0.0;
            $price = round($feed_price * (1 + $markup_percentage / 100), 4);
            $stock_status_id = isset($data['stock_status_id']) ? (int)$data['stock_status_id'] : $default_stock_status_id;
            $manufacturer_id = isset($data['manufacturer_id']) ? (int)$data['manufacturer_id'] : 0;
            $shipping = isset($data['shipping']) ? (int)$data['shipping'] : 1;
            $tax_class_id = isset($data['tax_class_id']) ? (int)$data['tax_class_id'] : $default_tax_class_id;
            $date_available = isset($data['date_available']) ? $this->db->escape($data['date_available']) : date('Y-m-d');
            $weight = isset($data['weight']) ? (float)$data['weight'] : 0;
            $weight_class_id = isset($data['weight_class_id']) ? (int)$data['weight_class_id'] : $default_weight_class_id;
            $length = isset($data['length']) ? (float)$data['length'] : 0;
            $width = isset($data['width']) ? (float)$data['width'] : 0;
            $height = isset($data['height']) ? (float)$data['height'] : 0;
            $length_class_id = isset($data['length_class_id']) ? (int)$data['length_class_id'] : $default_length_class_id;
            $subtract = isset($data['subtract']) ? (int)$data['subtract'] : 1;
            $minimum = isset($data['minimum']) ? (int)$data['minimum'] : 1;
            $sort_order = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;
            $status = isset($data['status']) ? (int)$data['status'] : 1;

            $product_values[] = "(
                '" . $model . "', 
                '" . $this->db->escape($sku) . "', 
                '" . (isset($data['upc']) ? $this->db->escape($data['upc']) : '') . "', 
                '" . (isset($data['ean']) ? $this->db->escape($data['ean']) : '') . "', 
                '" . (isset($data['jan']) ? $this->db->escape($data['jan']) : '') . "', 
                '" . (isset($data['isbn']) ? $this->db->escape($data['isbn']) : '') . "', 
                '" . (isset($data['mpn']) ? $this->db->escape($data['mpn']) : '') . "', 
                '" . (isset($data['location']) ? $this->db->escape($data['location']) : '') . "', 
                " . $quantity . ", 
                " . $stock_status_id . ", 
                '" . $image . "', 
                " . $manufacturer_id . ", 
                " . $shipping . ", 
                " . $price . ", 
                " . (isset($data['points']) ? (int)$data['points'] : 0) . ", 
                " . $tax_class_id . ", 
                '" . $date_available . "', 
                " . $weight . ", 
                " . $weight_class_id . ", 
                " . $length . ", 
                " . $width . ", 
                " . $height . ", 
                " . $length_class_id . ", 
                " . $subtract . ", 
                " . $minimum . ", 
                " . $sort_order . ", 
                " . $status . ", 
                NOW(), 
                NOW()
            )";
        }

        if (!empty($product_values)) {
            $sql_insert_product = "INSERT INTO " . DB_PREFIX . "product (
                model, sku, upc, ean, jan, isbn, mpn, location, quantity, stock_status_id, image, manufacturer_id, 
                shipping, price, points, tax_class_id, date_available, weight, weight_class_id, length, width, height, 
                length_class_id, subtract, minimum, sort_order, status, date_added, date_modified
            ) VALUES " . implode(", ", $product_values);
            
            $this->_executeQuery($sql_insert_product);

            if (!empty($product_skus_for_id_retrieval)) {
                if ($this->test_mode) {
                    // In test mode, the main product INSERT was logged but not executed.
                    // Simulate product IDs for the SKUs that would have been inserted.
                    foreach ($product_skus_for_id_retrieval as $original_sku_to_map) {
                        $new_product_ids_map[$original_sku_to_map] = $this->current_dummy_product_id++;
                        $this->log->write("MultiFeed Syncer (Test Mode): Assigning dummy product_id " . $new_product_ids_map[$original_sku_to_map] . " for SKU: " . $original_sku_to_map);
                    }
                } else {
                    // Real mode: fetch actual new IDs
                    $escaped_sku_list_for_query = [];
                    foreach($product_skus_for_id_retrieval as $original_sku_for_query) {
                        $escaped_sku_list_for_query[] = "'" . $this->db->escape($original_sku_for_query) . "'";
                    }
                    $sku_string_for_query = implode(",", $escaped_sku_list_for_query);
                    
                    $query_new_ids = $this->_executeQuery("SELECT product_id, sku FROM " . DB_PREFIX . "product WHERE sku IN (" . $sku_string_for_query . ")");
                    foreach ($query_new_ids->rows as $row) {
                        // $row['sku'] here is the value from the DB, which should match the original SKU
                        $new_product_ids_map[$row['sku']] = (int)$row['product_id']; 
                    }
                }
            }
        }
        
        // --- Начало: Обработка на product_description, product_to_store, url_alias ---
        $product_description_values = [];
        $product_to_store_values = [];
        $url_alias_values = [];

        foreach ($new_product_ids_map as $sku => $new_product_id) {
            if (!$new_product_id) continue; // Skip if product_id was not retrieved
            $product_data = $products_to_add[$sku];

            $product_to_store_values[] = "(" . (int)$new_product_id . ", 0)";

            foreach ($languages as $language) {
                $lang_id = (int)$language['language_id'];
                $name = 'N/A';
                $description = '';
                $meta_title = 'N/A';
                $meta_description = '';
                $meta_keyword = '';
                $tag = '';
                
                $desc_data_source = null;
                if (isset($product_data['product_description'][$lang_id])) {
                    $desc_data_source = $product_data['product_description'][$lang_id];
                } elseif (isset($product_data['product_description'][$default_language_id])) { 
                    $desc_data_source = $product_data['product_description'][$default_language_id];
                }

                if ($desc_data_source) {
                    $name = $this->db->escape(isset($desc_data_source['name']) ? $desc_data_source['name'] : 'N/A');
                    $description = $this->db->escape(isset($desc_data_source['description']) ? $desc_data_source['description'] : '');
                    $meta_title = $this->db->escape(isset($desc_data_source['meta_title']) ? $desc_data_source['meta_title'] : $name);
                    $meta_description = $this->db->escape(isset($desc_data_source['meta_description']) ? $desc_data_source['meta_description'] : '');
                    $meta_keyword = $this->db->escape(isset($desc_data_source['meta_keyword']) ? $desc_data_source['meta_keyword'] : '');
                    $tag = $this->db->escape(isset($desc_data_source['tag']) ? $desc_data_source['tag'] : '');
                }
                
                $product_description_values[] = "(
                    " . (int)$new_product_id . ", 
                    " . $lang_id . ", 
                    '" . $name . "', 
                    '" . $description . "', 
                    '" . $tag . "', 
                    '" . $meta_title . "', 
                    '" . $meta_description . "', 
                    '" . $meta_keyword . "'
                )";
            }

            $seo_name_base = '';
            if (isset($product_data['product_description'][$default_language_id]['name']) && !empty(trim($product_data['product_description'][$default_language_id]['name'])) && trim($product_data['product_description'][$default_language_id]['name']) !== 'N/A') {
                $seo_name_base = $product_data['product_description'][$default_language_id]['name'];
            } elseif (isset($products_to_add[$sku]['product_description'][$default_language_id]['name']) && !empty(trim($products_to_add[$sku]['product_description'][$default_language_id]['name'])) && trim($products_to_add[$sku]['product_description'][$default_language_id]['name']) !== 'N/A') {
                 $seo_name_base = $products_to_add[$sku]['product_description'][$default_language_id]['name'];
            }

            if(!empty(trim($seo_name_base))) {
                $keyword = $this->_generateSeoKeyword($seo_name_base);
                $original_keyword = $keyword;
                $counter = 1;
                $unique_keyword_found = false;
                while (!$unique_keyword_found) {
                    $query_check_keyword = $this->_executeQuery("SELECT url_alias_id FROM " . DB_PREFIX . "url_alias WHERE keyword = '" . $this->db->escape($keyword) . "'");
                    if ($query_check_keyword->num_rows == 0) {
                        $unique_keyword_found = true;
                    } else {
                        $keyword = $original_keyword . '-' . $counter++;
                    }
                }
                if ($unique_keyword_found) {
                    $url_alias_values[] = "('product_id=" . (int)$new_product_id . "', '" . $this->db->escape($keyword) . "')";
                }
            }
        }

        if (!empty($product_description_values)) {
            $sql_insert_desc = "INSERT INTO " . DB_PREFIX . "product_description (product_id, language_id, name, description, tag, meta_title, meta_description, meta_keyword) VALUES " . implode(", ", $product_description_values);
            $this->_executeQuery($sql_insert_desc);
        }

        if (!empty($product_to_store_values)) {
            $sql_insert_store = "INSERT INTO " . DB_PREFIX . "product_to_store (product_id, store_id) VALUES " . implode(", ", $product_to_store_values);
            $this->_executeQuery($sql_insert_store);
        }
        
        if (!empty($url_alias_values)) {
            $sql_insert_alias = "INSERT INTO " . DB_PREFIX . "url_alias (query, keyword) VALUES " . implode(", ", $url_alias_values);
            $this->_executeQuery($sql_insert_alias);
        }
        // --- Край: Обработка на product_description, product_to_store, url_alias ---

        return $new_product_ids_map;
    }

    /**
     * Пакетно актуализиране на цена и количество на продукти.
     * @param array $products_to_update Масив от продуктови данни [product_id => product_data]
     * @param float $markup_percentage Процентна надценка
     * @param string $identifier_field Полето, което съдържа уникалния идентификатор (sku)
     * @return int Брой успешно актуализирани продукти
     */
    private function _batchUpdateProducts(array $products_to_update, float $markup_percentage, string $identifier_field) {
        if (empty($products_to_update)) {
            return 0;
        }

        $successfully_updated_count = 0;
        $chunk_size = 150; // Размер на порцията за обновяване, може да се направи конфигурируем

        $product_chunks = array_chunk($products_to_update, $chunk_size, true); // true запазва ключовете (product_id)

        foreach ($product_chunks as $chunk) {
            $product_ids_in_chunk = [];
            $price_cases = [];
            $quantity_cases = [];

            foreach ($chunk as $product_id => $data) {
                $product_id = (int)$product_id;
                if (!$product_id) {
                    $this->log->write("MultiFeed Syncer: Пропуснат продукт за актуализация поради невалиден product_id в _batchUpdateProducts (chunk processing). Данни: " . json_encode($data));
                    continue;
                }

                $product_ids_in_chunk[] = $product_id;

                // Подготовка на CASE за цена
                $feed_price = isset($data['price']) ? (float)$data['price'] : null;
                if ($feed_price !== null) {
                    $calculated_price = round($feed_price * (1 + $markup_percentage / 100), 4);
                    $price_cases[] = "WHEN " . $product_id . " THEN '" . (float)$calculated_price . "'";
                } else {
                    $this->log->write("MultiFeed Syncer: Липсва цена от фийда за продукт ID: {$product_id} ('" . (isset($data[$identifier_field]) ? $this->db->escape($data[$identifier_field]) : 'N/A') . "') в текущата порция. Цената няма да бъде включена в CASE ъпдейта за този продукт.");
                }

                // Подготовка на CASE за количество
                $quantity = isset($data['quantity']) ? (int)$data['quantity'] : null;
                if ($quantity !== null) {
                    $quantity_cases[] = "WHEN " . $product_id . " THEN '" . (int)$quantity . "'";
                } else {
                    $this->log->write("MultiFeed Syncer: Липсва количество от фийда за продукт ID: {$product_id} ('" . (isset($data[$identifier_field]) ? $this->db->escape($data[$identifier_field]) : 'N/A') . "') в текущата порция. Количеството няма да бъде включено в CASE ъпдейта за този продукт.");
                }
            }

            if (empty($product_ids_in_chunk)) {
                continue; // Няма валидни продукти в тази порция
            }

            $sql_parts = [];
            if (!empty($price_cases)) {
                $sql_parts[] = "price = (CASE product_id " . implode(" ", $price_cases) . " ELSE price END)";
            }
            if (!empty($quantity_cases)) {
                $sql_parts[] = "quantity = (CASE product_id " . implode(" ", $quantity_cases) . " ELSE quantity END)";
            }

            // Ако няма какво да се актуализира за тази порция (нито цени, нито количества)
            if (empty($sql_parts)) {
                 $this->log->write("MultiFeed Syncer: Няма данни за актуализация (цена/количество) за порция с ID-та: " . implode(', ', $product_ids_in_chunk));
                continue;
            }

            $sql = "UPDATE " . DB_PREFIX . "product SET ";
            $sql .= implode(", ", $sql_parts);
            $sql .= ", date_modified = NOW()"; // Винаги обновяваме date_modified
            $sql .= " WHERE product_id IN (" . implode(",", $product_ids_in_chunk) . ")";

            try {
                $this->_executeQuery($sql);
                // В OpenCart $this->db->countAffected() връща броя засегнати редове.
                // В тестов режим _executeQuery връща true, така че countAffected няма да е налично директно.
                // Затова ще приемем, че ако заявката не хвърли грешка, всички продукти в порцията са "обработени".
                // Броят на *действително променените* стойности може да е по-малък, ако новите стойности са същите като старите.
                $affected_rows = $this->test_mode ? count($product_ids_in_chunk) : $this->db->countAffected();
                $successfully_updated_count += $affected_rows; // или count($product_ids_in_chunk) ако countAffected не е надеждно след CASE

                if ($this->test_mode && $affected_rows > 0) {
                    $this->log->write("MultiFeed Syncer (Test Mode): Симулирано успешно обновяване на " . $affected_rows . " продукта в порция. SQL: " . $sql);
                }

            } catch (Exception $e) {
                // Логване на грешката за цялата порция
                $this->log->write("MultiFeed Syncer: Грешка при пакетно актуализиране на порция продукти. IDs: (" . implode(",", $product_ids_in_chunk) . "). Грешка: " . $e->getMessage() . " SQL: " . $sql);
            }
        }

        return $successfully_updated_count;
    }
}
?>