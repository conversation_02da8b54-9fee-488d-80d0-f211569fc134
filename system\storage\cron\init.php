<?php

$opencart_root_path = '/home/<USER>/public_html/';

// Зареждане на OpenCart средата
require_once $opencart_root_path . 'admin/config.php';

if(!isset($_SERVER['SERVER_PORT'])) {
    $_SERVER['SERVER_PORT'] = 443;
    $_SERVER['HTTPS'] = true;
}

require_once DIR_SYSTEM . 'startup.php';


// Инициализация на OpenCart registry
$registry = new Registry();

// Зареждане на конфигурацията
$config = new Config();
$config->load('default');
$config->load('catalog');
$registry->set('config', $config);

// Log
$log = new Log( 'multi_feed_syncer.log' );
$registry->set('log', $log);


// Event
$event = new Event($registry);
$registry->set('event', $event);

// Event Register
if ($config->has('action_event')) {
	foreach ($config->get('action_event') as $key => $value) {
		$event->register($key, new Action($value));
	}
}

// Зареждане на базата данни
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Зареждане на loader
$loader = new Loader($registry);
$registry->set('load', $loader);

// Зареждане на URL
$registry->set('url', new Url(HTTP_SERVER, HTTPS_SERVER));

// Language
$language = new Language($config->get('language_default'));
$language->load($config->get('language_default'));
$registry->set('language', $language);

// Config Autoload
if ($config->has('config_autoload')) {
	foreach ($config->get('config_autoload') as $value) {
		$loader->config($value);
	}
}

// Language Autoload
if ($config->has('language_autoload')) {
	foreach ($config->get('language_autoload') as $value) {
		$loader->language($value);
	}
}

// Library Autoload
if ($config->has('library_autoload')) {
	foreach ($config->get('library_autoload') as $value) {
		$loader->library($value);
	}
}

// Model Autoload
if ($config->has('model_autoload')) {
	foreach ($config->get('model_autoload') as $value) {
		$loader->model($value);
	}
}

$_language = $registry->get('db')->query("SELECT * FROM " . DB_PREFIX . "language WHERE code = '".$config->get('language_default')."'");
if($_language->num_rows > 0){
    $config->set('config_language_id', $_language->row['language_id']);
}

require_once __DIR__ . '/startup.php';






