<?php
class ModelExtensionModuleMultiFeedSyncerImageDownloader extends Model {

    private $downloadSubdir = '';
    private $log;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->log = new Log('multi_feed_syncer_image_downloader.log');
    }

    /**
     * Задава името на подпапката за изтегляне на изображения.
     *
     * @param string $subdirName Името на папката (напр. 'eoffice').
     */
    public function setDownloadSubdir($subdirName) {
        // Изчистваме името на папката от нежелани символи за сигурност
        $sanitizedDir = trim(preg_replace('/[^a-zA-Z0-9_-]/', '', $subdirName));

        // Уверяваме се, че името не е празно след изчистването
        if (!empty($sanitizedDir)) {
            $this->downloadSubdir = $sanitizedDir . '/';
        }
    }

    public function getMainDownloadPath() {
        return DIR_IMAGE . 'catalog/';
    }

    /**
     * Изтегля изображения от списък с URL адреси, записва ги локално и връща карта на съответствията.
     *
     * @param array $image_urls Масив от уникални URL адреси на изображения за изтегляне.
     * @return array Асоциативен масив [original_url => new_relative_path].
     */
    public function downloadAndProcessImages($image_urls, $subdirName=null) {
        if (empty($image_urls)) {
            return [];
        }

        if($subdirName) {
            $this->setDownloadSubdir($subdirName);
        }

        $this->_ensureDirectoryExists();
        
        $downloaded_map = [];

        // 1. Инициализация на cURL multi-handle
        $mh = curl_multi_init();
        $handles = []; // Ще пазим връзката между URL и cURL handle

        foreach ($image_urls as $url) {
            // Пропускаме невалидни URL-и или файлове, които вече съществуват локално
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                if (isset($this->log)) $this->log->write("Image Downloader: Пропуснат невалиден URL: " . $url);
                continue;
            }

            $local_relative_path = $this->_generateUniqueLocalPath($url);
            if (is_file($this->getMainDownloadPath() . $local_relative_path)) {
                $downloaded_map[$url] = 'catalog/'.$local_relative_path;
                continue;
            }

            // 2. Създаване и конфигуриране на индивидуални cURL handles за всеки URL
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);     // Връща резултата като стринг
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);     // Следва пренасочвания
            curl_setopt($ch, CURLOPT_MAXREDIRS, 5);             // Ограничение на пренасочванията
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);       // Timeout за свързване
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);              // Общ timeout за изпълнение
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);    // Често се налага за работа с различни SSL сертификати
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            curl_multi_add_handle($mh, $ch);
            $handles[$url] = $ch;
        }

        if (empty($handles)) {
            return $downloaded_map; // Всички изображения вече са съществували
        }
        
        // 3. Изпълнение на паралелните заявки
        $running = null;
        do {
            curl_multi_exec($mh, $running);
            // Изчакваме активност по някой от сокетите, за да не товарим процесора
            if ($running) {
                curl_multi_select($mh);
            }
        } while ($running > 0);

        // 4. Обработка на резултатите и запис на файловете
        foreach ($handles as $url => $ch) {
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if ($http_code == 200) {
                $content = curl_multi_getcontent($ch);
                $local_relative_path = $this->_generateUniqueLocalPath($url);
                
                if (file_put_contents($this->getMainDownloadPath() . $local_relative_path, $content)) {
                    $downloaded_map[$url] = 'catalog/'.$local_relative_path;
                } else {
                    if (isset($this->log)) $this->log->write("Image Downloader: Неуспешен ЗАПИС на файл: " . $this->getMainDownloadPath() . $local_relative_path);
                }
            } else {
                if (isset($this->log)) $this->log->write("Image Downloader: Неуспешно ИЗТЕГЛЯНЕ с cURL: " . $url . " (HTTP Code: " . $http_code . ")");
            }

            // 5. Почистване
            curl_multi_remove_handle($mh, $ch);
        }

        curl_multi_close($mh);

        return $downloaded_map;
    }

    /**
     * Генерира уникален локален път за даден URL.
     * @param string $url
     * @return string
     */
    private function _generateUniqueLocalPath($url) {
        $path_info = pathinfo(parse_url($url, PHP_URL_PATH));
        $extension = isset($path_info['extension']) ? strtolower($path_info['extension']) : 'jpg';

        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($extension, $allowed_extensions)) {
            return false;
        }

        $filename = basename($url);

        return $this->downloadSubdir . $filename;
    }

    /**
     * Проверява дали целевата директория съществува и я създава, ако е необходимо.
     */
    private function _ensureDirectoryExists() {
        $full_path = $this->getMainDownloadPath() . $this->downloadSubdir;
        if (!is_dir($full_path)) {
            mkdir($full_path, 0755, true);
        }
    }
}