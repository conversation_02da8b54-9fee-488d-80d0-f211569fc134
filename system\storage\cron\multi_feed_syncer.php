<?php

$log_file = __DIR__ . '/multi_feed_syncer.log';
// Скрипт, който се извиква от cron задача
// Примерна cron команда: /usr/bin/php /път/до/вашия/opencart/system/storage/cron/multi_feed_syncer.php >> /път/до/вашия/opencart/system/storage/logs/multi_feed_syncer.log 2>&1 [cite: 25, 26]

// Задаване на по-дълго време за изпълнение
set_time_limit(0);
ini_set('memory_limit', '2048M'); // Коригирайте при нужда

require_once(__DIR__ . '/init.php');

// --- Проверка дали скриптът е извикан от CLI ---
if (php_sapi_name() !== 'cli') {
    die("Този скрипт може да бъде стартиран само от командния ред (CLI).");
}

file_put_contents($log_file, "[" . date("Y-m-d H:i:s") . "] Скриптът е извикан от CLI.\n", FILE_APPEND);

// --- Извикване на метода на контролера ---
// Зареждане на контролера
// Подаваме $registry, за да може контролерът да има достъп до всички Opencart обекти
try {
    // $controller_instance = new ControllerExtensionModuleMultiFeedSyncer($registry); // Директно инстанциране
    $registry->get('load')->controller('extension/module/multi_feed_syncer/startSyncProcess'); // Директно инстанциране
    // file_put_contents($log_file, "[" . date("Y-m-d H:i:s") . "] controller_instance: " . get_class($controller_instance) . "\n", FILE_APPEND);
    // $controller_instance->startSyncProcess(); // Извикване на метода
} catch (\Exception $e) {
    file_put_contents($log_file, "[" . date("Y-m-d H:i:s") . "] Exception: " . $e->getMessage() . "\n", FILE_APPEND);
}

echo "[" . date("Y-m-d H:i:s") . "] Cron скриптът приключи изпълнението.\n";
file_put_contents($log_file, "[" . date("Y-m-d H:i:s") . "] Cron скриптът приключи изпълнението.\n", FILE_APPEND);
?>