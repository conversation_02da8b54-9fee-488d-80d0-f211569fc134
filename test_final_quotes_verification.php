<?php
/**
 * Финален тест за проверка на корекцията за запазване на кавички
 * Тества цялостния процес от XML до интерфейс
 */

// Включваме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Зареждаме config
$config = new Config();
$registry->set('config', $config);

// Зареждаме модела
$loader = new Loader($registry);
$registry->set('load', $loader);

// Зареждаме eOffice конектора
$loader->model('extension/module/multi_feed_syncer_connectors/eoffice');
$eoffice_connector = $registry->get('model_extension_module_multi_feed_syncer_connectors_eoffice');

// Зареждаме основния Multi Feed Syncer модел
$loader->model('extension/module/multi_feed_syncer');
$main_model = $registry->get('model_extension_module_multi_feed_syncer');

echo "<h1>🔍 Финален тест за проверка на корекцията за запазване на кавички</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
    .code { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; border-radius: 3px; }
    .summary { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    .pass { background: #d4edda; border-color: #c3e6cb; color: #155724; }
    .fail { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
</style>\n";

// Реални тестови случаи със специални символи
$test_cases = [
    [
        'name' => 'Монитор с инчове',
        'xml' => '<CategoryBranch><BG>Компютри|Монитори|24" LED монитори</BG></CategoryBranch>',
        'expected' => 'Компютри > Монитори > 24" LED монитори'
    ],
    [
        'name' => 'Телевизор с инчове',
        'xml' => '<CategoryBranch><BG>Електроника|Телевизори|55" Smart TV</BG></CategoryBranch>',
        'expected' => 'Електроника > Телевизори > 55" Smart TV'
    ],
    [
        'name' => 'HTML entities кавички',
        'xml' => '<CategoryBranch><BG>Принтери &gt; A4 &quot;All-in-One&quot; устройства</BG></CategoryBranch>',
        'expected' => 'Принтери > A4 "All-in-One" устройства'
    ],
    [
        'name' => 'Единични кавички',
        'xml' => '<CategoryBranch><BG>Категория с \'единични\' кавички</BG></CategoryBranch>',
        'expected' => 'Категория с \'единични\' кавички'
    ],
    [
        'name' => 'Смесени символи',
        'xml' => '<CategoryBranch><BG>Gaming > Монитори 24" & 27" модели</BG></CategoryBranch>',
        'expected' => 'Gaming > Монитори 24" & 27" модели'
    ]
];

$total_tests = 0;
$passed_tests = 0;

echo "<div class='test-section'>\n";
echo "<h2>🧪 Тест на XML обработката в конектора</h2>\n";

echo "<table>\n";
echo "<tr><th>Тестов случай</th><th>Оригинален XML</th><th>Обработен резултат</th><th>Очакван резултат</th><th>Статус</th></tr>\n";

foreach ($test_cases as $test_case) {
    $total_tests++;
    
    // Обвиваме във валиден XML документ
    $full_xml = '<?xml version="1.0" encoding="UTF-8"?>' . $test_case['xml'];
    
    // Конвертираме с нашия метод
    $converted = $eoffice_connector->convertXMLdataToArray($full_xml);
    
    // Извличаме стойността
    $result = $converted['CategoryBranch']['BG'] ?? '';
    
    // Заменяме разделителя "|" с " > " както прави конектора
    $processed_result = str_replace('|', ' > ', $result);
    
    // Проверяваме дали резултатът съвпада с очаквания
    $test_passed = $processed_result === $test_case['expected'];
    
    if ($test_passed) {
        $passed_tests++;
    }
    
    echo "<tr>\n";
    echo "<td><strong>" . htmlspecialchars($test_case['name']) . "</strong></td>\n";
    echo "<td><div class='code'>" . htmlspecialchars($test_case['xml']) . "</div></td>\n";
    echo "<td><div class='code'>" . htmlspecialchars($processed_result) . "</div></td>\n";
    echo "<td><div class='code'>" . htmlspecialchars($test_case['expected']) . "</div></td>\n";
    echo "<td class='" . ($test_passed ? 'success' : 'error') . "'>" . ($test_passed ? '✅ УСПЕХ' : '❌ ГРЕШКА') . "</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>💾 Тест на запазване в основния модул</h2>\n";

$test_mfsc_id = 999;

// Изчистваме стари тестови данни
$db->query("DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");

echo "<table>\n";
echo "<tr><th>Source категория</th><th>Target категория</th><th>Запазено успешно</th><th>Статус</th></tr>\n";

$save_tests = 0;
$save_passed = 0;

foreach ($test_cases as $test_case) {
    $save_tests++;
    
    try {
        $mapping_id = $main_model->saveCategoryMapping($test_mfsc_id, $test_case['expected'], 'Тестова категория');
        $save_passed++;
        
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['expected']) . "</td>\n";
        echo "<td>Тестова категория</td>\n";
        echo "<td>ДА (ID: {$mapping_id})</td>\n";
        echo "<td class='success'>✅ УСПЕХ</td>\n";
        echo "</tr>\n";
    } catch (Exception $e) {
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['expected']) . "</td>\n";
        echo "<td>Тестова категория</td>\n";
        echo "<td>НЕ</td>\n";
        echo "<td class='error'>❌ ГРЕШКА: " . htmlspecialchars($e->getMessage()) . "</td>\n";
        echo "</tr>\n";
    }
}

echo "</table>\n";
echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>📖 Тест на извличане от основния модул</h2>\n";

$retrieved_mappings = $main_model->getCategoriesMapping($test_mfsc_id);

echo "<table>\n";
echo "<tr><th>Оригинална категория</th><th>Извлечена категория</th><th>Съвпадение</th><th>Кавички запазени</th><th>Статус</th></tr>\n";

$retrieve_tests = 0;
$retrieve_passed = 0;

foreach ($test_cases as $test_case) {
    $retrieve_tests++;
    
    $found = false;
    foreach ($retrieved_mappings as $mapping) {
        if ($mapping['source_category_path'] === $test_case['expected']) {
            $found = true;
            $has_quotes_original = strpos($test_case['expected'], '"') !== false;
            $has_quotes_retrieved = strpos($mapping['source_category_path'], '"') !== false;
            
            $quotes_preserved = !$has_quotes_original || $has_quotes_retrieved;
            $exact_match = $mapping['source_category_path'] === $test_case['expected'];
            
            $test_passed = $exact_match && $quotes_preserved;
            
            if ($test_passed) {
                $retrieve_passed++;
            }
            
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($test_case['expected']) . "</td>\n";
            echo "<td>" . htmlspecialchars($mapping['source_category_path']) . "</td>\n";
            echo "<td>" . ($exact_match ? 'ДА' : 'НЕ') . "</td>\n";
            echo "<td>" . ($quotes_preserved ? 'ДА' : 'НЕ') . "</td>\n";
            echo "<td class='" . ($test_passed ? 'success' : 'error') . "'>" . ($test_passed ? '✅ УСПЕХ' : '❌ ГРЕШКА') . "</td>\n";
            echo "</tr>\n";
            break;
        }
    }
    
    if (!$found) {
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($test_case['expected']) . "</td>\n";
        echo "<td>НЕ НАМЕРЕНА</td>\n";
        echo "<td>НЕ</td>\n";
        echo "<td>НЕ</td>\n";
        echo "<td class='error'>❌ ГРЕШКА - Не е намерена</td>\n";
        echo "</tr>\n";
    }
}

echo "</table>\n";
echo "</div>\n";

// Изчистваме тестовите данни
$db->query("DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");

// Обобщение на резултатите
$total_all_tests = $total_tests + $save_tests + $retrieve_tests;
$total_passed = $passed_tests + $save_passed + $retrieve_passed;
$success_rate = $total_all_tests > 0 ? round(($total_passed / $total_all_tests) * 100, 1) : 0;

echo "<div class='summary " . ($success_rate >= 90 ? 'pass' : 'fail') . "'>\n";
echo "<h2>📊 Обобщение на резултатите</h2>\n";
echo "<table>\n";
echo "<tr><th>Категория тестове</th><th>Изпълнени</th><th>Успешни</th><th>Процент успех</th></tr>\n";
echo "<tr><td>XML обработка</td><td>{$total_tests}</td><td>{$passed_tests}</td><td>" . ($total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 1) : 0) . "%</td></tr>\n";
echo "<tr><td>Запазване в модула</td><td>{$save_tests}</td><td>{$save_passed}</td><td>" . ($save_tests > 0 ? round(($save_passed / $save_tests) * 100, 1) : 0) . "%</td></tr>\n";
echo "<tr><td>Извличане от модула</td><td>{$retrieve_tests}</td><td>{$retrieve_passed}</td><td>" . ($retrieve_tests > 0 ? round(($retrieve_passed / $retrieve_tests) * 100, 1) : 0) . "%</td></tr>\n";
echo "<tr><td><strong>ОБЩО</strong></td><td><strong>{$total_all_tests}</strong></td><td><strong>{$total_passed}</strong></td><td><strong>{$success_rate}%</strong></td></tr>\n";
echo "</table>\n";

if ($success_rate >= 90) {
    echo "<h3>🎉 КОРЕКЦИЯТА Е УСПЕШНА!</h3>\n";
    echo "<p>Всички тестове преминават успешно. Символът за инчове (\") се запазва правилно в цялата верига от XML до база данни.</p>\n";
} else {
    echo "<h3>⚠️ КОРЕКЦИЯТА СЕ НУЖДАЕ ОТ ДОПЪЛНИТЕЛНА РАБОТА</h3>\n";
    echo "<p>Някои тестове не преминават успешно. Моля, проверете грешките по-горе.</p>\n";
}

echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>📋 Резюме на корекциите</h2>\n";
echo "<p>Направените корекции включват:</p>\n";
echo "<ul>\n";
echo "<li><strong>convertXMLdataToArray():</strong> Добавени JSON_UNESCAPED_UNICODE и JSON_UNESCAPED_SLASHES флагове</li>\n";
echo "<li><strong>_getXmlNodeValue():</strong> Добавено html_entity_decode() за запазване на специални символи</li>\n";
echo "<li><strong>_convertDomNodeToArray():</strong> Подобрена обработка на textContent с html_entity_decode()</li>\n";
echo "<li><strong>decodeCategoryPath():</strong> Разширено декодиране на HTML entities в основния модул</li>\n";
echo "<li><strong>normalizeTextForComparison():</strong> Добавено декодиране преди нормализация</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
