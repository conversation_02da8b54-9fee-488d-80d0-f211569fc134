-- SQL код за създаване на таблица за съответствията на категориите
-- Този файл съдържа SQL кода за създаване на таблицата multi_feed_syncer_categories_mapping

CREATE TABLE IF NOT EXISTS `{{prefix}}multi_feed_syncer_categories_mapping` (
  `mapping_id` int(11) NOT NULL AUTO_INCREMENT,
  `mfsc_id` int(11) NOT NULL,
  `source_category_path` TEXT NOT NULL,
  `target_category_path` TEXT NOT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`mapping_id`),
  KEY `mfsc_id` (`mfsc_id`),
  KEY `source_category_path` (`source_category_path`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Описание на полетата:
-- mapping_id: Уникален идентификатор на записа
-- mfsc_id: ID на конектора (връзка към multi_feed_syncer_connectors)
-- source_category_path: Пълен път на категорията от конектора (формат: "главна > подкатегория > подкатегория")
-- target_category_path: Съответстващ път на категорията от сайта (същия формат)
-- date_added: Дата на създаване на записа
-- date_modified: Дата на последна модификация на записа
