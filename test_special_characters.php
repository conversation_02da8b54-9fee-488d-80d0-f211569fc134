<?php
/**
 * Тестов скрипт за проверка на специални символи в Multi Feed Syncer
 * Тества дали символът " (кавички) и други специални символи се запазват правилно
 */

// Включваме OpenCart framework
require_once('config.php');
require_once(DIR_SYSTEM . 'startup.php');

// Стартираме registry
$registry = new Registry();

// Зареждаме database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Зареждаме config
$config = new Config();
$registry->set('config', $config);

// Зареждаме модела
$loader = new Loader($registry);
$registry->set('load', $loader);

// Зареждаме Multi Feed Syncer модела
$loader->model('extension/module/multi_feed_syncer');
$model = $registry->get('model_extension_module_multi_feed_syncer');

// Тестови случаи със специални символи
$test_cases = [
    'Монитори > 24" LED монитори > Samsung',
    'Телевизори > 55" Smart TV > LG',
    'Принтери > A4 "All-in-One" устройства',
    'Компютри > 15.6" лаптопи > Dell',
    'Аудио > 5.1" звукови системи',
    'Офис > Хартия A4 "Premium" качество',
    'Мебели > Маси 120" x 80" размер',
    'Електроника > TV 32" & 40" модели',
    'Категория с \'единични\' кавички',
    'Категория с <тагове> и &амперсанд',
    'Смесени символи: "кавички" & \'апострофи\' > тест'
];

echo "<h1>Тест на специални символи в Multi Feed Syncer</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
</style>\n";

echo "<table>\n";
echo "<tr>
    <th>Тестов случай</th>
    <th>Оригинал</th>
    <th>Декодиран</th>
    <th>Съдържа кавички</th>
    <th>Специални символи</th>
    <th>Статус</th>
</tr>\n";

foreach ($test_cases as $test_case) {
    $results = $model->testSpecialCharacters($test_case);
    
    // Проверяваме дали кавичките се запазват
    $quotes_preserved = $results['special_chars']['quotes'] > 0;
    $status_class = $quotes_preserved ? 'success' : 'error';
    $status_text = $quotes_preserved ? 'УСПЕХ' : 'ГРЕШКА';
    
    // Ако няма кавички в оригинала, проверяваме дали има други специални символи
    if (!strpos($test_case, '"')) {
        $has_special = $results['special_chars']['single_quotes'] > 0 || 
                      $results['special_chars']['ampersand'] > 0 || 
                      $results['special_chars']['greater_than'] > 0 ||
                      $results['special_chars']['less_than'] > 0;
        
        if ($has_special) {
            $status_class = 'success';
            $status_text = 'УСПЕХ';
        } else {
            $status_class = 'warning';
            $status_text = 'БЕЗ СПЕЦИАЛНИ';
        }
    }
    
    echo "<tr>\n";
    echo "<td>" . htmlspecialchars($test_case) . "</td>\n";
    echo "<td>" . htmlspecialchars($results['original']) . "</td>\n";
    echo "<td>" . htmlspecialchars($results['decoded']) . "</td>\n";
    echo "<td>" . ($results['contains_quotes'] ? 'ДА' : 'НЕ') . "</td>\n";
    echo "<td>";
    echo "\" : " . $results['special_chars']['quotes'] . ", ";
    echo "' : " . $results['special_chars']['single_quotes'] . ", ";
    echo "& : " . $results['special_chars']['ampersand'] . ", ";
    echo "> : " . $results['special_chars']['greater_than'] . ", ";
    echo "< : " . $results['special_chars']['less_than'];
    echo "</td>\n";
    echo "<td class='{$status_class}'>{$status_text}</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

// Тест за запазване и извличане от базата данни
echo "<h2>Тест за запазване в базата данни</h2>\n";

$test_mfsc_id = 999; // Тестов ID
$test_source = 'Тест > 24" монитори > Samsung';
$test_target = 'TV, Монитори, Видео и Аудио';

try {
    // Запазваме тестовото съответствие
    $mapping_id = $model->saveCategoryMapping($test_mfsc_id, $test_source, $test_target);
    
    // Извличаме обратно
    $mappings = $model->getCategoriesMapping($test_mfsc_id);
    
    $found = false;
    foreach ($mappings as $mapping) {
        if ($mapping['source_category_path'] === $test_source) {
            $found = true;
            echo "<p class='success'>УСПЕХ: Категорията се запази и извлече правилно!</p>\n";
            echo "<p><strong>Запазено:</strong> " . htmlspecialchars($mapping['source_category_path']) . "</p>\n";
            echo "<p><strong>Съдържа кавички:</strong> " . (strpos($mapping['source_category_path'], '"') !== false ? 'ДА' : 'НЕ') . "</p>\n";
            break;
        }
    }
    
    if (!$found) {
        echo "<p class='error'>ГРЕШКА: Категорията не се намери или се корумпира!</p>\n";
    }
    
    // Изчистваме тестовите данни
    $db->query("DELETE FROM `" . DB_PREFIX . "multi_feed_syncer_categories_mapping` WHERE `mfsc_id` = '" . (int)$test_mfsc_id . "'");
    
} catch (Exception $e) {
    echo "<p class='error'>ГРЕШКА при тестване: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Заключение</h2>\n";
echo "<p>Този тест проверява дали специални символи като кавички (\") се запазват правилно в Multi Feed Syncer модула.</p>\n";
echo "<p>Ако всички тестове показват 'УСПЕХ', значи корекцията работи правилно.</p>\n";

?>
