<?php

require_once DIR_APPLICATION . 'controller/extension/module/dev_functions.php';

class ControllerExtensionModuleMultiFeedSyncer extends Controller {

    
    // Път до конекторите [cite: 3]
    const CONNECTOR_PATH = DIR_APPLICATION . 'model/extension/module/multi_feed_syncer_connectors/'; 

    public function __construct($registry) {
        parent::__construct($registry);   
        $this->error = array();
    }

    public function index() {
        $this->load->language('extension/module/multi_feed_syncer');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('setting/setting'); // За настройки, ако има
        $this->load->model('extension/module/multi_feed_syncer');

        // Стандартно запазване на настройки в Opencart (ако се добавят глобални по-късно)
        // if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            // $this->model_setting_setting->editSetting('module_multi_feed_syncer', $this->request->post);
            // $this->session->data['success'] = $this->language->get('text_success');
            // $this->response->redirect($this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true));
        // }
        
        // Зареждане на езикови променливи за шаблона
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_edit'] = $this->language->get('text_edit');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');
        $data['text_copied'] = $this->language->get('text_copied');
        $data['text_processing'] = $this->language->get('text_processing');
        $data['text_new_connectors'] = $this->language->get('text_new_connectors'); // [cite: 13]
        $data['text_activate'] = $this->language->get('text_activate'); // [cite: 14]
        $data['text_logs_title'] = $this->language->get('text_logs_title'); // [cite: 20]
        $data['text_link_works'] = $this->language->get('text_link_works'); // [cite: 8]
        $data['text_link_fails'] = $this->language->get('text_link_fails'); // [cite: 8]
        $data['text_success_status'] = $this->language->get('text_success_status');
        $data['text_fail_status'] = $this->language->get('text_fail_status');
        $data['text_status_unknown'] = $this->language->get('text_status_unknown');


        $data['tab_suppliers'] = $this->language->get('tab_suppliers'); // [cite: 6]
        $data['tab_logs'] = $this->language->get('tab_logs'); // [cite: 6]

        
        
        // Колони за таблицата с доставчици [cite: 8]
        $data['column_supplier'] = $this->language->get('column_supplier');
        $data['column_connection_type'] = $this->language->get('column_connection_type');
        $data['column_action'] = $this->language->get('column_action');
        $data['column_status'] = $this->language->get('column_status');
        
        // Колони за таблицата с логове [cite: 22]
        $data['column_date'] = $this->language->get('column_date');
        $data['column_log_supplier'] = $this->language->get('column_supplier'); // Преизползване на езиковата променлива
        $data['column_connection_status'] = $this->language->get('column_connection_status');
        $data['column_duration'] = $this->language->get('column_duration');
        $data['column_information'] = $this->language->get('column_information');

        $data['button_check_connection'] = $this->language->get('button_check_connection'); // [cite: 8]
        $data['button_copy_cron_text'] = $this->language->get('button_copy_cron_text');
        $data['tooltip_copy_cron'] = $this->language->get('tooltip_copy_cron');
        $data['text_cron_copy_error'] = $this->language->get('text_cron_copy_error');

        $data['button_manual_sync'] = $this->language->get('button_manual_sync');

        // Нови езикови променливи за разширената функционалност
        $data['text_last_file_download'] = $this->language->get('text_last_file_download');
        $data['text_file_not_downloaded'] = $this->language->get('text_file_not_downloaded');
        $data['text_categories_mapping'] = $this->language->get('text_categories_mapping');
        $data['text_source_category'] = $this->language->get('text_source_category');
        $data['text_target_category'] = $this->language->get('text_target_category');
        $data['text_new_category'] = $this->language->get('text_new_category');
        $data['text_search_categories'] = $this->language->get('text_search_categories');
        $data['text_no_categories_found'] = $this->language->get('text_no_categories_found');
        $data['text_categories_mapping_saved'] = $this->language->get('text_categories_mapping_saved');
        $data['text_start_products_update'] = $this->language->get('text_start_products_update');
        $data['text_products_update_success'] = $this->language->get('text_products_update_success');
        $data['text_products_update_error'] = $this->language->get('text_products_update_error');
        $data['button_download_file_again'] = $this->language->get('button_download_file_again');
        $data['button_edit_mapping'] = $this->language->get('button_edit_mapping');
        $data['button_save_mappings'] = $this->language->get('button_save_mappings');
        $data['button_start_update'] = $this->language->get('button_start_update');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        // Хлебни трохи (Breadcrumbs)
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_extension'),
            'href' => $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('extension/module/multi_feed_syncer', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('extension/module/multi_feed_syncer', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true);
        $data['user_token'] = $this->session->data['user_token'];

        // --- Таб "Доставчици" --- [cite: 7]
        $data['activated_suppliers'] = array();
        $activated_connectors_db = $this->model_extension_module_multi_feed_syncer->getActivatedConnectors();
        
        foreach ($activated_connectors_db as $connector_db) {
            $connector_file = self::CONNECTOR_PATH . $connector_db['connector_key'] . '.php';
            if (file_exists($connector_file)) {
                $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_db['connector_key']);
                $connector_model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_db['connector_key'];
                
                $connection_type = '';
                if (is_callable(array($this->$connector_model_name, 'getConnectionType'))) {
                     $connection_type = $this->$connector_model_name->getConnectionType(); // [cite: 8]
                }
                
                $data['activated_suppliers'][] = array(
                    'mfsc_id'         => $connector_db['mfsc_id'],
                    'name'            => $connector_db['connector'],
                    'connector_key'   => $connector_db['connector_key'],
                    'connection_type' => $connection_type,
                    // Статусът ще се актуализира чрез AJAX от checkConnection()
                );
            }
        }

        // Сканиране за нови конектори [cite: 12]
        $data['new_connectors'] = array();
        $connector_files = glob(self::CONNECTOR_PATH . '*.php');
        $activated_keys = array_column($activated_connectors_db, 'connector_key');

        if ($connector_files) {
            foreach ($connector_files as $file) {
                $connector_key = basename($file, '.php');

                if (!in_array($connector_key, $activated_keys)) {
                    // Зареждане на модела на конектора, за да се получат данните за инсталация
                    if (file_exists(self::CONNECTOR_PATH . $connector_key . '.php')) {
                        $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_key);
                        $connector_model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_key;

                        if (is_callable(array($this->$connector_model_name, 'getInstallData'))) { // [cite: 15]
                            $install_data = $this->$connector_model_name->getInstallData(); 

                            $data['new_connectors'][] = array(
                                'name'          => $install_data['connector'], 
                                'connector_key' => $install_data['connector_key']
                            );
                        }
                    }
                }
            }
        }


        // --- Таб "Логове" --- [cite: 19]
        $data['logs'] = array();
        if (isset($this->request->get['page'])) {
            $page = (int)$this->request->get['page'];
        } else {
            $page = 1;
        }
        $log_limit = 10; // [cite: 21]

        $filter_data = array(
            'start' => ($page - 1) * $log_limit,
            'limit' => $log_limit
        );

        $logs_total = $this->model_extension_module_multi_feed_syncer->getTotalLogs();
        $results = $this->model_extension_module_multi_feed_syncer->getLogs($filter_data);

        foreach ($results as $result) {
            $process_data_display = $this->language->get('text_no_results'); // По подразбиране
            if (!empty($result['process_data'])) {
                $process_data_array = @json_decode($result['process_data'], true);
                if($process_data_array === false){
                    $process_data_array = [];
                }
                // Зареждане на специфичния модел на конектора за форматиране на данните от лога [cite: 23]
                $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($result['mfsc_id']);
                if ($connector_info && file_exists(self::CONNECTOR_PATH . $connector_info['connector_key'] . '.php')) {
                    $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_info['connector_key']);
                    $c_model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_info['connector_key'];

                    if(is_callable(array($this->$c_model_name, 'getSyncLogData')) && $process_data_array){
                        $process_data_display = $this->$c_model_name->getSyncLogData($process_data_array);
                    } else {
                        $process_data_display = '';
                    }
                } else {
                    $process_data_display = print_r($process_data_array, true); // Резервен вариант, ако конекторът липсва
                }
            }

            $data['logs'][] = array(
                'date'       => date($this->language->get('datetime_format'), strtotime($result['process_date'])),
                'supplier'   => $result['connector'],
                'connection_status' => $result['connection_status'] ? $this->language->get('text_success_status') : $this->language->get('text_fail_status'),
                'duration'   => $result['process_timing'] . ' сек.',
                'information'=> $process_data_display
            );
        }
        
        // Пагинация за логовете [cite: 21]
        $pagination = new Pagination();
        $pagination->total = $logs_total;
        $pagination->page = $page;
        $pagination->limit = $log_limit;
        $pagination->url = $this->url->link('extension/module/multi_feed_syncer', 'user_token=' . $this->session->data['user_token'] . '&page={page}', true);
        $data['pagination'] = $pagination->render();
        $data['results_count'] = sprintf($this->language->get('text_pagination'), ($logs_total) ? (($page - 1) * $log_limit) + 1 : 0, ((($page - 1) * $log_limit) > ($logs_total - $log_limit)) ? $logs_total : ((($page - 1) * $log_limit) + $log_limit), $logs_total, ceil($logs_total / $log_limit));


        // Проверка за разработчик и зареждане на Dev лог таб
        $data['is_developer'] = false; // По подразбиране
        if (function_exists('isDeveloper') && isDeveloper()) {

            $data['button_clear_dev_log'] = $this->language->get('button_clear_dev_log');
            $data['text_confirm_clear_dev_log'] = $this->language->get('text_confirm_clear_dev_log');
            $data['text_processing'] = $this->language->get('text_processing'); // Ако вече не е заредена

            $data['is_developer'] = true;
            $data['tab_dev_logs'] = $this->language->get('tab_dev_logs'); // Зареждане на текста за таба
            $dev_log_file = DIR_LOGS . 'developer_log.txt'; // Името на файла, дефинирано в LogDeveloper()
            if (file_exists($dev_log_file) && is_readable($dev_log_file)) {
                // Прочитане на съдържанието на файла
                $log_content_raw = file_get_contents($dev_log_file);
                // Показване на последните записи първи (по-удобно за логове)
                $log_lines = explode(PHP_EOL, trim($log_content_raw));
                $data['dev_log_content'] = implode(PHP_EOL, $log_lines);

                // Ако искате да ограничите броя на показаните редове:
                // $max_lines = 500; // Например
                // $log_lines_limited = array_slice($log_lines_reversed, 0, $max_lines);
                // $data['dev_log_content'] = implode(PHP_EOL, $log_lines_limited);
                // if (count($log_lines_reversed) > $max_lines) {
                //     $data['dev_log_content'] .= PHP_EOL . PHP_EOL . "... (показани са последните {$max_lines} от " . count($log_lines_reversed) . " реда) ...";
                // }

            } else {
                $data['dev_log_content'] = 'Файлът с логове за разработчици (' . basename($dev_log_file) . ') не е намерен или не може да бъде прочетен.';
            }
        } else {
            // Ако не е разработчик, тези променливи не са нужни за шаблона,
            // но ги дефинираме за всеки случай, за да избегнем undefined index грешки
            $data['tab_dev_logs'] = '';
            $data['dev_log_content'] = '';
        }



            


        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('extension/module/multi_feed_syncer', $data));
    }

    public function manualSync() {
        $this->load->language('extension/module/multi_feed_syncer');
        $json = array();

        // Проверка на права
        if (!$this->user->hasPermission('modify', 'extension/module/multi_feed_syncer')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->post['mfsc_id'])) {
                $mfsc_id = (int)$this->request->post['mfsc_id'];

                $this->load->model('extension/module/multi_feed_syncer');
                $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);

                if ($connector_info && file_exists(self::CONNECTOR_PATH . $connector_info['connector_key'] . '.php')) {
                    $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_info['connector_key']);
                    $c_model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_info['connector_key'];

                    if (is_callable([$this->$c_model_name, 'processSupplierFeed'])) {
                        try {
                            $start_time = microtime(true);
                            $sync_stats = $this->$c_model_name->processSupplierFeed(
                                $connector_info['connector_key'],
                                $mfsc_id,
                                false, // не е тестов режим
                                $this->model_extension_module_multi_feed_syncer
                            );
                            $end_time = microtime(true);
                            $execution_time = round($end_time - $start_time, 2);

                            // Записване на лог за синхронизацията
                            $this->model_extension_module_multi_feed_syncer->addSynchronizationLog(
                                $mfsc_id,
                                $sync_stats,
                                $execution_time
                            );

                            $json['success'] = $this->language->get('text_manual_sync_success');
                            $json['stats'] = array(
                                'added' => isset($sync_stats['added']) ? $sync_stats['added'] : 0,
                                'updated' => isset($sync_stats['updated']) ? $sync_stats['updated'] : 0,
                                'skipped' => isset($sync_stats['skipped']) ? $sync_stats['skipped'] : 0,
                                'errors' => isset($sync_stats['errors']) ? $sync_stats['errors'] : 0,
                                'execution_time' => $execution_time
                            );

                            if (function_exists('LogDeveloper')) {
                                LogDeveloper('Ръчна синхронизация за конектор ' . $connector_info['connector'] . ' (ID: ' . $mfsc_id . ') от потребител: ' . $this->user->getUserName());
                            }
                        } catch (Exception $e) {
                            $json['error'] = $this->language->get('text_manual_sync_error') . ': ' . $e->getMessage();
                        }
                    } else {
                        $json['error'] = $this->language->get('text_manual_sync_error') . ': Методът processSupplierFeed не е намерен.';
                    }
                } else {
                    $json['error'] = $this->language->get('text_manual_sync_error') . ': Конекторът не е намерен.';
                }
            } else {
                $json['error'] = $this->language->get('text_manual_sync_error') . ': Липсва ID на конектор.';
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    /**
     * AJAX: Проверява статуса на връзката за доставчик. [cite: 8]
     */
    public function checkConnection() {
        $json = array();
        $this->load->language('extension/module/multi_feed_syncer');

        if (isset($this->request->get['connector_id'])) {
            $mfsc_id = (int)$this->request->get['connector_id'];
            $this->load->model('extension/module/multi_feed_syncer');
            $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);

            if ($connector_info && file_exists(self::CONNECTOR_PATH . $connector_info['connector_key'] . '.php')) {
                $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_info['connector_key']);
                $model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_info['connector_key'];
                if (is_callable(array($this->$model_name, 'checkConnection'))) {
                    $status = $this->$model_name->checkConnection();
                    $json['status'] = $status ? 1 : 0; // 1 за работи, 0 за не работи [cite: 8]
                    $json['message'] = $status ? $this->language->get('text_link_works') : $this->language->get('text_link_fails');
                } else {
                    $json['status'] = 0;
                    $json['message'] = 'Грешка: Методът checkConnection не е намерен в модела на конектора.';
                }
            } else {
                $json['status'] = 0;
                $json['message'] = 'Грешка: Конекторът не е намерен или файлът липсва.';
            }
        } else {
            $json['status'] = 0;
            $json['message'] = 'Грешка: ID на конектор не е предоставено.';
        }
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }



    /**
     * AJAX: Активира нов конектор. [cite: 16]
     */
    public function activateConnector() {
        $json = array();
        $this->load->language('extension/module/multi_feed_syncer');
        $this->load->model('extension/module/multi_feed_syncer');

        if (isset($this->request->post['connector_key'])) {
            $connector_key = $this->request->post['connector_key'];
            $connector_file = self::CONNECTOR_PATH . $connector_key . '.php';

            if (file_exists($connector_file)) {
                $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_key);
                $model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_key;

                if (is_callable(array($this->$model_name, 'getInstallData'))) { // [cite: 15]
                    $install_data = $this->$model_name->getInstallData();
                    
                    // Проверка дали вече съществува по ключ
                    $query_existing = $this->db->query("SELECT mfsc_id FROM " . DB_PREFIX . "multi_feed_syncer_connectors WHERE connector_key = '" . $this->db->escape($install_data['connector_key']) . "'");
                    if ($query_existing->num_rows) {
                         $json['error'] = 'Конекторът вече е активиран.';
                    } else {
                        $mfsc_id = $this->model_extension_module_multi_feed_syncer->addConnector($install_data); // [cite: 16]
                        $json['success'] = 'Конекторът е активиран успешно.';
                        $json['new_connector_data'] = array( // Данни за динамично добавяне в таблицата [cite: 17]
                            'mfsc_id' => $mfsc_id,
                            'name' => $install_data['connector'],
                            'connector_key' => $install_data['connector_key'],
                            'connection_type' => method_exists($this->$model_name, 'getConnectionType') ? $this->$model_name->getConnectionType() : ''
                        );
                    }
                } else {
                    $json['error'] = 'Грешка: Методът getInstallData не е намерен в модела на конектора.';
                }
            } else {
                $json['error'] = 'Грешка: Файлът на конектора не е намерен.';
            }
        } else {
            $json['error'] = 'Грешка: Ключ на конектор не е предоставен.';
        }
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Извиква се от Cron задача за стартиране на процеса на синхронизация. [cite: 24, 26]
     * Може да синхронизира всички активни конектори или конкретен, ако connector_key е подаден чрез CLI аргументи.
     */
    public function startSyncProcess() {

        // Стъпка 1: Проверка за сигурност при уеб достъп
        if (!$this->_performWebAccessSecurityCheck()) {
            return; // Прекратява изпълнението, ако проверката за сигурност не мине
        }

        // Стъпка 2: Зареждане на езикови файлове и основния модел на модула
        $this->load->language('extension/module/multi_feed_syncer');
        if (!isset($this->model_extension_module_multi_feed_syncer)) {
            $this->load->model('extension/module/multi_feed_syncer');
        }

        // Стъпка 3: Активиране на тестов режим (ако е указано)
        $this->_handleTestModeActivation();

        // Стъпка 4: Инициализиране на лог префикс и запис на начално съобщение
        $log_entry_prefix = ''; 
        $this->_logInitialMessages($log_entry_prefix);

        // Стъпка 5: Определяне на конекторите за обработка
        $connectors_to_process = $this->_determineConnectorsToProcess($log_entry_prefix);

        $this->writeToCronLog("Конекторите за обработка: " . print_r($connectors_to_process, true));

        if (empty($connectors_to_process)) {
            $this->_logFinalMessages($log_entry_prefix, true); // true показва, че приключваме преждевременно
            return;
        }

        $this->writeToCronLog("Конекторите за обработка: >>>");

        // Стъпка 6: Обработка на всеки определен конектор
        foreach ($connectors_to_process as $connector_db) {

            // $this->writeToCronLog("Конектор: " . print_r($connector_db, true));

            $this->_processSingleConnector($connector_db, $log_entry_prefix);

        }

        $this->writeToCronLog("Запис на финално съобщение");

        // Стъпка 7: Запис на финално съобщение
        $this->_logFinalMessages($log_entry_prefix);
    }



    /**
     * Този метод се извиква от cron job, за да обработи опашката с изображения.
     */
    public function processImageDownloadQueue() {
        // Зареждане на нужните модели
        $this->load->model('extension/module/multi_feed_syncer');
        $this->load->model('extension/module/multi_feed_syncer_image_downloader');
        $image_downloader = $this->model_extension_module_multi_feed_syncer_image_downloader;

        $this->log = new Log('multi_feed_syncer_image_downloader.log');

        // Вземане на порция задачи и заключването им
        $tasks = $this->model_extension_module_multi_feed_syncer->getAndLockPendingImages();

        if (empty($tasks)) {
            $this->log->write('Image Downloader Cron: Няма изображения в опашката за обработка.');
            return;
        }

        $this->log->write('Image Downloader Cron: Започва обработка на ' . count($tasks) . ' задачи.');
        
        // Групиране на задачите по конектор (mfsc_id), за да се ползва правилната папка
        $tasks_by_connector = [];
        foreach ($tasks as $task) {
            $tasks_by_connector[$task['mfsc_id']][] = $task;
        }

        $processed_tasks = [];
        $failed_task_ids = [];

        foreach ($tasks_by_connector as $mfsc_id => $connector_tasks) {
            // Вземане на connector_key, за да знаем името на папката
            $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);
            $connector_key = $connector_info ? $connector_info['connector_key'] : 'default';

            $this->log->write('Image Downloader Cron: Обработка на ' . count($connector_tasks) . ' задачи за конектор ' . $connector_key);

            $urls_to_download = array_unique(array_column($connector_tasks, 'image_url'));

            $this->log->write('Image Downloader Cron: Изтегляне на изображенията');


            // Изтегляне на изображенията

            if(!is_callable([$image_downloader, 'downloadAndProcessImages'])) {
                $this->log->write('Image Downloader Cron: downloadAndProcessImages не е валиден метод.');
                continue;
            }

            try {
                $downloaded_map = $image_downloader->downloadAndProcessImages($urls_to_download, $connector_key);
            } catch (Exception $e) {
                $this->log->write('Image Downloader Cron: Грешка при изтегляне на изображенията: ' . $e->getMessage());
                continue;
            }

            $this->log->write('Image Downloader Cron: Обработка на резултатите');
            
            // Обработка на резултатите
            foreach ($connector_tasks as $task) {
                if (isset($downloaded_map[$task['image_url']])) {
                    // Успешно изтеглено - добавяме новия път към задачата
                    $task['new_local_path'] = $downloaded_map[$task['image_url']];
                    $processed_tasks[] = $task;
                } else {
                    // Неуспешно
                    $failed_task_ids[] = $task['queue_id'];
                }
            }
        }

        $this->log->write('Image Downloader Cron: Обработка на продуктите с изтеглените изображения');
        $this->log->write('Image Downloader Cron: processed_tasks: ' . print_r($processed_tasks, true));
        
        // Актуализиране на продуктите с изтеглените изображения
        if (!empty($processed_tasks)) {
            $this->model_extension_module_multi_feed_syncer->updateProductsWithDownloadedImages($processed_tasks);
            $completed_ids = array_column($processed_tasks, 'queue_id');
            $this->model_extension_module_multi_feed_syncer->updateQueueTasks($completed_ids, 'completed');
            $this->log->write('Image Downloader Cron: Успешно обработени ' . count($completed_ids) . ' изображения.');
        }

        // Обработка на неуспешните задачи
        if (!empty($failed_task_ids)) {
            $this->model_extension_module_multi_feed_syncer->incrementQueueTaskAttempts($failed_task_ids);
            $this->log->write('Image Downloader Cron: ' . count($failed_task_ids) . ' изображения не успяха да бъдат изтеглени.');
        }

        $this->log->write('Image Downloader Cron: Обработка завършена.');
    }

    /**
     * Този метод се извиква от cron job за повторно опитване на failed изображения.
     */
    public function retryFailedImages() {
        // Зареждане на нужните модели
        $this->load->model('extension/module/multi_feed_syncer');
        $this->load->model('extension/module/multi_feed_syncer_image_downloader');
        $image_downloader = $this->model_extension_module_multi_feed_syncer_image_downloader;

        $this->log = new Log('multi_feed_syncer_failed_images_retry.log');

        // Вземане на порция failed задачи за повторно опитване
        $failed_tasks = $this->model_extension_module_multi_feed_syncer->getAndLockFailedImages(30, 5, 24);

        if (empty($failed_tasks)) {
            $this->log->write('Failed Images Retry Cron: Няма failed изображения за повторно опитване.');
            return;
        }

        $this->log->write('Failed Images Retry Cron: Започва повторно опитване на ' . count($failed_tasks) . ' failed изображения.');

        // Групиране на задачите по конектор (mfsc_id)
        $tasks_by_connector = [];
        foreach ($failed_tasks as $task) {
            $tasks_by_connector[$task['mfsc_id']][] = $task;
        }

        $processed_tasks = [];
        $failed_again_task_ids = [];

        foreach ($tasks_by_connector as $mfsc_id => $connector_tasks) {
            // Вземане на connector_key
            $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);
            $connector_key = $connector_info ? $connector_info['connector_key'] : 'default';

            $this->log->write('Failed Images Retry Cron: Повторно опитване на ' . count($connector_tasks) . ' задачи за конектор ' . $connector_key);

            $urls_to_download = array_unique(array_column($connector_tasks, 'image_url'));

            try {
                $downloaded_map = $image_downloader->downloadAndProcessImages($urls_to_download, $connector_key);
            } catch (Exception $e) {
                $this->log->write('Failed Images Retry Cron: Грешка при изтегляне: ' . $e->getMessage());
                // Маркираме всички задачи от този конектор като неуспешни отново
                $failed_again_task_ids = array_merge($failed_again_task_ids, array_column($connector_tasks, 'queue_id'));
                continue;
            }

            // Обработка на резултатите
            foreach ($connector_tasks as $task) {
                if (isset($downloaded_map[$task['image_url']])) {
                    // Успешно изтеглено
                    $task['new_local_path'] = $downloaded_map[$task['image_url']];
                    $processed_tasks[] = $task;
                    $this->log->write('Failed Images Retry Cron: Успешно изтеглено при повторен опит: ' . $task['image_url']);
                } else {
                    // Неуспешно отново
                    $failed_again_task_ids[] = $task['queue_id'];
                }
            }
        }

        // Актуализиране на продуктите с успешно изтеглените изображения
        if (!empty($processed_tasks)) {
            $this->model_extension_module_multi_feed_syncer->updateProductsWithDownloadedImages($processed_tasks);
            $completed_ids = array_column($processed_tasks, 'queue_id');
            $this->model_extension_module_multi_feed_syncer->updateQueueTasks($completed_ids, 'completed');
            $this->log->write('Failed Images Retry Cron: Успешно обработени при повторен опит ' . count($completed_ids) . ' изображения.');
        }

        // Обработка на отново неуспешните задачи
        if (!empty($failed_again_task_ids)) {
            $this->model_extension_module_multi_feed_syncer->incrementQueueTaskAttempts($failed_again_task_ids);
            $this->log->write('Failed Images Retry Cron: ' . count($failed_again_task_ids) . ' изображения отново не успяха да бъдат изтеглени.');
        }

        $this->log->write('Failed Images Retry Cron: Повторното опитване завършено.');
    }

    /**
     * Административен метод за ръчно рестартиране на failed изображения.
     * Може да се извика от админ панела.
     */
    public function manualRetryFailedImages() {
        $this->load->model('extension/module/multi_feed_syncer');

        $json = [];

        try {
            // Вземаме всички failed изображения с attempts < 5
            $failed_query = $this->model_extension_module_multi_feed_syncer->_executeQuery("
                SELECT COUNT(*) as total
                FROM `" . DB_PREFIX . "product_image_download_queue`
                WHERE `status` = 'failed' AND `attempts` < 5
            ");

            $total_failed = $failed_query->row['total'];

            if ($total_failed == 0) {
                $json['success'] = 'Няма failed изображения за рестартиране.';
            } else {
                // Рестартираме failed изображенията
                $this->model_extension_module_multi_feed_syncer->_executeQuery("
                    UPDATE `" . DB_PREFIX . "product_image_download_queue`
                    SET `status` = 'pending', `date_added` = NOW()
                    WHERE `status` = 'failed' AND `attempts` < 5
                ");

                $json['success'] = "Успешно рестартирани {$total_failed} failed изображения. Те ще бъдат обработени при следващото стартиране на cron задачата.";
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при рестартиране на failed изображения: ' . $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Административен метод за получаване на статистика за изображенията в опашката.
     */
    public function getImageQueueStats() {
        $this->load->model('extension/module/multi_feed_syncer');

        $json = [];

        try {
            $stats_query = $this->model_extension_module_multi_feed_syncer->_executeQuery("
                SELECT
                    `status`,
                    COUNT(*) as count,
                    AVG(`attempts`) as avg_attempts
                FROM `" . DB_PREFIX . "product_image_download_queue`
                GROUP BY `status`
            ");

            $stats = [];
            $total = 0;

            foreach ($stats_query->rows as $row) {
                $stats[$row['status']] = [
                    'count' => (int)$row['count'],
                    'avg_attempts' => round((float)$row['avg_attempts'], 2)
                ];
                $total += (int)$row['count'];
            }

            $json['success'] = true;
            $json['stats'] = $stats;
            $json['total'] = $total;

        } catch (Exception $e) {
            $json['error'] = 'Грешка при извличане на статистика: ' . $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    private function writeToCronLog($message) {
        $log_file = DIR_LOGS . 'multi_feed_syncer.log'; // [cite: 26]
        file_put_contents($log_file, $message . PHP_EOL, FILE_APPEND);
    }

    // --- Начало на помощни методи за startSyncProcess ---

    private function _performWebAccessSecurityCheck() {
        if (php_sapi_name() !== 'cli') {
            // Проверка дали е извикано през уеб от разработчик
            if (!function_exists('isDeveloper') || !isDeveloper()) {
                $this->log('MultiFeed Syncer: Неоторизиран уеб достъп до startSyncProcess от IP: ' . (isset($this->request->server['REMOTE_ADDR']) ? $this->request->server['REMOTE_ADDR'] : 'N/A'));
                // Можете да върнете HTTP 403 Forbidden или просто да прекратите
                // header('HTTP/1.1 403 Forbidden');
                // echo 'Достъпът е забранен.';
                return false; // Прекратява изпълнението
            }
            // Ако е разработчик, записваме в лога, че е стартирано ръчно през уеб
            $this->log('MultiFeed Syncer: startSyncProcess е стартиран ръчно през УЕБ от разработчик IP: ' . (isset($this->request->server['REMOTE_ADDR']) ? $this->request->server['REMOTE_ADDR'] : 'N/A'));
        }
        return true;
    }

    private function _handleTestModeActivation() {
        $enable_test_mode = false;

        if (php_sapi_name() === 'cli') {
            global $argv;
            if (isset($argv[1]) && ($argv[1] === 'test' || $argv[1] === '--test')) {
                $enable_test_mode = true;
            }
        } else { 
            // Уеб достъп - само за разработчици (проверката isDeveloper() вече е минала в _performWebAccessSecurityCheck)
            if (isset($this->request->get['testmode']) && $this->request->get['testmode'] == '1') {
                $enable_test_mode = true;
            }
        }
        
        if ($enable_test_mode) {
            $this->model_extension_module_multi_feed_syncer->setTestMode(true);
            $log_message = date('Y-m-d H:i:s') . " - MultiFeed Syncer: ТЕСТОВ РЕЖИМ АКТИВИРАН.\n";
            if (method_exists($this, 'writeToCronLog')) {
                $this->writeToCronLog($log_message);
            }
            // Показва съобщение в конзолата или на разработчика през уеб
            if (php_sapi_name() === 'cli' || (function_exists('isDeveloper') && isDeveloper())) {
                echo $log_message; 
            }

            $this->log($log_message);

        } else {
            $this->model_extension_module_multi_feed_syncer->setTestMode(false);
        }
    }

    private function _logInitialMessages(&$log_entry_prefix) {
        $log_entry_prefix = "[" . date("Y-m-d H:i:s") . "] MultiFeedSyncer: ";
        $message = $log_entry_prefix . "Процесът на синхронизация стартира.\n";
        
        if (method_exists($this, 'writeToCronLog')) {
            $this->writeToCronLog($message);
        }
        if (php_sapi_name() === 'cli' || (function_exists('isDeveloper') && isDeveloper())) {
            echo $message;
        }
    }

    private function _determineConnectorsToProcess(&$log_entry_prefix) {
        $connector_key_to_sync = null;

        if (php_sapi_name() === 'cli') {
            global $argv;
            $arg_index_for_key = 1;
            if (isset($argv[1]) && ($argv[1] === 'test' || $argv[1] === '--test')) {
                $arg_index_for_key = 2; // Ако първият е 'test', ключът е вторият
            }
            if (isset($argv[$arg_index_for_key])) {
                $connector_key_to_sync = $argv[$arg_index_for_key];
            }
        } elseif (function_exists('isDeveloper') && isDeveloper()) { 
            if (isset($this->request->get['connector_key'])) {
                $connector_key_to_sync = $this->request->get['connector_key'];
            }
        }

        $connectors_to_process = [];
        if ($connector_key_to_sync) {
            // Проверяваме дали моделът е зареден, преди да го използваме
                if (!isset($this->model_extension_module_multi_feed_syncer)) {
                $this->load->model('extension/module/multi_feed_syncer');
            }
            $connector_data = $this->model_extension_module_multi_feed_syncer->getConnectorByKey($connector_key_to_sync);
            if ($connector_data && $connector_data['status']) { // Проверяваме и статуса
                $connectors_to_process[] = $connector_data;
            } else {
                $message = $log_entry_prefix . "Конектор с ключ '{$connector_key_to_sync}' не е намерен или не е активен.\n";
                if (method_exists($this, 'writeToCronLog')) $this->writeToCronLog($message);
                if (php_sapi_name() === 'cli' || (function_exists('isDeveloper') && isDeveloper())) echo $message;
            }
        } else {
                if (!isset($this->model_extension_module_multi_feed_syncer)) {
                $this->load->model('extension/module/multi_feed_syncer');
            }
            $connectors_to_process = $this->model_extension_module_multi_feed_syncer->getActivatedConnectors();
            if (empty($connectors_to_process)) {
                $message = $log_entry_prefix . "Няма активни конектори, конфигурирани в системата за обработка.\n";
                    if (method_exists($this, 'writeToCronLog')) $this->writeToCronLog($message);
                if (php_sapi_name() === 'cli' || (function_exists('isDeveloper') && isDeveloper())) echo $message;
            }
        }
        return $connectors_to_process;
    }

    private function _processSingleConnector($connector_db, $log_entry_prefix) {
        $current_log_connector = ""; // Лог специфичен за този конектор
        $mfsc_id = $connector_db['mfsc_id'];
        $c_key = $connector_db['connector_key'];
        $connector_name = $connector_db['connector']; // Име на конектора за логване
        $connector_file = DIR_APPLICATION . 'model/extension/module/multi_feed_syncer_connectors/' . $c_key . '.php'; // Пълен път

        $current_log_connector .= $log_entry_prefix . "Обработка на конектор: {$connector_name} (Ключ: {$c_key})\n";
        $start_time = microtime(true);
        $sync_stats = ['added' => 0, 'updated' => 0, 'skipped' => 0, 'deleted' => 0, 'errors' => 0, 'message' => '', 'error_message' => ''];

        if (file_exists($connector_file)) {
            $this->load->model('extension/module/multi_feed_syncer_connectors/' . $c_key);
            $c_model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $c_key;
            if (is_callable([$this->$c_model_name, 'processSupplierFeed'])) {
                try {
                    $sync_stats = $this->$c_model_name->processSupplierFeed($c_key, $mfsc_id, $this->model_extension_module_multi_feed_syncer->getTestMode(), $this->model_extension_module_multi_feed_syncer);
                    $current_log_connector .= $this->$c_model_name->getCurrentLog();
                } catch (Exception $e) {
                    $sync_stats['errors']++;
                    $sync_stats['error_message'] = "Критична грешка при обработка на конектор {$c_key}: " . $e->getMessage();
                    $current_log_connector .= $log_entry_prefix . $sync_stats['error_message'] . "\n";
                }
            } else {
                $sync_stats['errors']++;
                $sync_stats['error_message'] = "Задължителният метод processSupplierFeed липсва в {$c_key} конектора.";
                $current_log_connector .= $log_entry_prefix . $sync_stats['error_message'] . "\n";
            }
        } else {
            $sync_stats['errors']++;
            $sync_stats['error_message'] = "Файлът на конектора {$connector_file} не е намерен.";
            $current_log_connector .= $log_entry_prefix . $sync_stats['error_message'] . "\n";
        }

        $end_time = microtime(true);
        $execution_time = round($end_time - $start_time, 2);

        $current_log_connector .= $log_entry_prefix . "Статистика за '{$connector_name}': ";
        $current_log_connector .= "Добавени: " . (isset($sync_stats['added']) ? $sync_stats['added'] : 'N/A') . ", ";
        $current_log_connector .= "Актуализирани: " . (isset($sync_stats['updated']) ? $sync_stats['updated'] : 'N/A') . ", ";
        $current_log_connector .= "Пропуснати: " . (isset($sync_stats['skipped']) ? $sync_stats['skipped'] : 'N/A') . ", ";
        // $current_log_connector .= "Изтрити: " . (isset($sync_stats['deleted']) ? $sync_stats['deleted'] : 'N/A') . ", "; // Ако имате изтриване
        $current_log_connector .= "Грешки: " . (isset($sync_stats['errors']) ? $sync_stats['errors'] : 'N/A') . ". ";
        $current_log_connector .= "Време за изпълнение: {$execution_time} сек.\n";
        
        if (!empty($sync_stats['message'])) {
                $current_log_connector .= $log_entry_prefix . "Съобщение от '{$connector_name}': " . $sync_stats['message'] . "\n";
        }
        if (!empty($sync_stats['error_message']) && $sync_stats['errors'] > 0) { // Показваме error_message само ако има грешки
                $current_log_connector .= $log_entry_prefix . "Детайли за грешка от '{$connector_name}': " . $sync_stats['error_message'] . "\n";
        }

        if (is_callable([$this, 'writeToCronLog'])) {
            $this->writeToCronLog($current_log_connector);
        }
        if (php_sapi_name() === 'cli' || (function_exists('isDeveloper') && isDeveloper())) {
            echo $current_log_connector;
        }

        // Записване на лог за синхронизацията в таблица multi_feed_syncer_logs
        $this->model_extension_module_multi_feed_syncer->addSynchronizationLog(
            $mfsc_id,
            $sync_stats, 
            $execution_time 
        );

    }

    private function _logFinalMessages($log_entry_prefix, $premature_exit = false) {
        $message = "";
        if ($premature_exit) {
            $message = $log_entry_prefix . "Процесът на синхронизация приключи преждевременно (няма конектори за обработка).\n";
        } else {
            $message = $log_entry_prefix . "Процесът на синхронизация приключи.\n";
        }

        if (is_callable([$this, 'writeToCronLog'])) {
            $this->writeToCronLog($message);
        }
        if (php_sapi_name() === 'cli' || (function_exists('isDeveloper') && isDeveloper())) {
            echo $message;
        }
    }

    // --- Край на помощни методи за startSyncProcess ---

    protected function validate() {
        if (!$this->user->hasPermission('modify', 'extension/module/multi_feed_syncer')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        return !$this->error;
    }
    
    public function install() {
        $this->load->model('extension/module/multi_feed_syncer');
        $this->model_extension_module_multi_feed_syncer->install(); // Създаване на таблиците [cite: 3]
    }

    public function uninstall() {
        $this->load->model('extension/module/multi_feed_syncer');
        $this->model_extension_module_multi_feed_syncer->uninstall();
        // По желание може да се изтрият и настройките
        // $this->load->model('setting/setting');
        // $this->model_setting_setting->deleteSetting('module_multi_feed_syncer');
    }

    /**
     * Метод за запис в лога за разработчици от контролера.
     * Използва глобалната функция LogDeveloper.
     *
     * @param mixed $message Съобщението за запис.
     */
    public function log_dev($message) {
        LogDeveloper($message); // Извикваме глобалната функция
    }

    public function log($message) {
        Log_($message); // Извикваме глобалната функция
    }

    public function clearDevLog() {
        $this->load->language('extension/module/multi_feed_syncer');
        $json = array();
    
        // Проверяваме дали функцията isDeveloper съществува и дали потребителят е разработчик
        if (function_exists('isDeveloper') && isDeveloper()) {
            $dev_log_file = DIR_LOGS . 'developer_log.txt'; // Името на файла, използвано в LogDeveloper()
    
            if (file_exists($dev_log_file)) {
                if (is_writable($dev_log_file)) {
                    // Изчистваме съдържанието на файла
                    if (file_put_contents($dev_log_file, '') !== false) {
                        $json['success'] = $this->language->get('text_dev_log_cleared_success');
                        // По желание: записваме в самия лог, че е бил изчистен
                        if (function_exists('LogDeveloper')) {
                            LogDeveloper("Dev логът беше изчистен от потребител: " . $this->user->getUserName() . " (IP: " . (isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'N/A') . ")");
                        }
                    } else {
                        $json['error'] = $this->language->get('text_dev_log_clear_error') . ' (Не може да се запише във файла)';
                    }
                } else {
                    $json['error'] = $this->language->get('text_dev_log_clear_error') . ' (Файлът не е достъпен за писане)';
                }
            } else {
                // Ако файлът не съществува, той на практика е "чист"
                $json['success'] = $this->language->get('text_dev_log_cleared_success') . ' (Файлът не съществуваше)';
            }
        } else {
            $json['error'] = $this->language->get('error_permission'); // Грешка за липса на права
        }
    
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: Получава информация за последното изтегляне на файлове за конектор
     */
    public function getLastFileDownloadInfo() {
        $json = [];
        $this->load->language('extension/module/multi_feed_syncer');

        if (isset($this->request->get['mfsc_id'])) {
            $mfsc_id = (int)$this->request->get['mfsc_id'];
            $this->load->model('extension/module/multi_feed_syncer');
            $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);

            if ($connector_info && file_exists(self::CONNECTOR_PATH . $connector_info['connector_key'] . '.php')) {
                $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_info['connector_key']);
                $model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_info['connector_key'];

                if (is_callable(array($this->$model_name, 'getLastFileDownloadInfo'))) {
                    $file_info = $this->$model_name->getLastFileDownloadInfo();
                    $json['success'] = true;
                    $json['file_info'] = $file_info;
                } else {
                    $json['error'] = 'Методът getLastFileDownloadInfo не е намерен в конектора.';
                }
            } else {
                $json['error'] = 'Конекторът не е намерен.';
            }
        } else {
            $json['error'] = 'Липсва ID на конектор.';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: Изтегля отново файла за конектор
     */
    public function downloadFileAgain() {
        $json = [];
        $this->load->language('extension/module/multi_feed_syncer');

        if (!$this->user->hasPermission('modify', 'extension/module/multi_feed_syncer')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->post['mfsc_id'])) {
                $mfsc_id = (int)$this->request->post['mfsc_id'];
                $this->load->model('extension/module/multi_feed_syncer');
                $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);

                if ($connector_info && file_exists(self::CONNECTOR_PATH . $connector_info['connector_key'] . '.php')) {
                    $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_info['connector_key']);
                    $model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_info['connector_key'];

                    if (is_callable(array($this->$model_name, 'downloadFileAgain'))) {
                        try {
                            $result = $this->$model_name->downloadFileAgain();
                            if ($result) {
                                $json['success'] = 'Файлът е изтеглен успешно.';
                            } else {
                                $json['error'] = 'Грешка при изтегляне на файла.';
                            }
                        } catch (Exception $e) {
                            $json['error'] = 'Грешка при изтегляне: ' . $e->getMessage();
                        }
                    } else {
                        $json['error'] = 'Методът downloadFileAgain не е намерен в конектора.';
                    }
                } else {
                    $json['error'] = 'Конекторът не е намерен.';
                }
            } else {
                $json['error'] = 'Липсва ID на конектор.';
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Нормализира текст за сравнение на категории
     * @param string $text Текст за нормализиране
     * @return string Нормализиран текст
     */
    private function normalizeTextForComparison($text) {
        // Първо декодираме HTML entities
        $decoded = $this->decodeCategoryPath($text);

        // След това правим lowercase
        return mb_strtolower(trim($decoded), 'UTF-8');
    }

    /**
     * Декодира HTML entities в пътищата на категории (копие от модела)
     * @param string $category_path Път на категория
     * @return string Декодиран път
     */
    private function decodeCategoryPath($category_path) {
        if (empty($category_path)) {
            return $category_path;
        }

        // Декодираме HTML entities (особено &gt; -> >, &quot; -> ", &amp; -> &)
        $decoded = html_entity_decode($category_path, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Допълнително почистване на възможни останали entities
        $decoded = str_replace([
            '&gt;',   // > символ
            '&lt;',   // < символ
            '&amp;',  // & символ
            '&quot;', // " символ (двойни кавички)
            '&#39;',  // ' символ (единични кавички)
            '&#34;'   // " символ (алтернативен код)
        ], [
            '>',
            '<',
            '&',
            '"',
            "'",
            '"'
        ], $decoded);

        return trim($decoded);
    }

    /**
     * Извлича името на последната част от път на категория
     * @param string $category_path Пълен път на категория (например "Хартия > Формуляри > А4")
     * @return string Име на последната част (например "А4")
     */
    private function getLastCategoryName($category_path) {
        $parts = explode(' > ', trim($category_path));
        return trim(end($parts));
    }

    /**
     * Изчислява дълбочината на категория (брой нива)
     * @param string $category_path Път на категория
     * @return int Брой нива (подкатегории)
     */
    private function getCategoryDepth($category_path) {
        if (empty(trim($category_path))) {
            return 0;
        }
        return count(explode(' > ', trim($category_path)));
    }

    /**
     * Проверява дали два пътя на категории имат общо начало
     * @param string $path1 Първи път
     * @param string $path2 Втори път
     * @return string|false Общото начало или false ако няма
     */
    private function getCommonCategoryPrefix($path1, $path2) {
        $parts1 = explode(' > ', trim($path1));
        $parts2 = explode(' > ', trim($path2));

        $common_parts = [];
        $min_length = min(count($parts1), count($parts2));

        for ($i = 0; $i < $min_length; $i++) {
            $normalized_part1 = $this->normalizeTextForComparison($parts1[$i]);
            $normalized_part2 = $this->normalizeTextForComparison($parts2[$i]);

            if ($normalized_part1 === $normalized_part2) {
                $common_parts[] = trim($parts1[$i]);
            } else {
                break;
            }
        }

        return count($common_parts) > 0 ? implode(' > ', $common_parts) : false;
    }

    /**
     * Сравнява две съвпадения и избира най-подходящото според правилата за приоритет
     * @param array|null $match1 Първо съвпадение
     * @param array|null $match2 Второ съвпадение
     * @return array|null Най-доброто съвпадение
     */
    private function chooseBestMatch($match1, $match2) {
        // Ако едното е null, връщаме другото
        if (!$match1) return $match2;
        if (!$match2) return $match1;

        $path1 = $match1['path'];
        $path2 = $match2['path'];

        // Проверяваме дали имат общо начало
        $common_prefix = $this->getCommonCategoryPrefix($path1, $path2);

        if ($common_prefix) {
            // Имат общо начало - избираме по-дълбоката категория
            $depth1 = $this->getCategoryDepth($path1);
            $depth2 = $this->getCategoryDepth($path2);

            if ($depth1 > $depth2) {
                // Първата е по-дълбока
                $match1['match_info_details'] = "Избрана поради по-голяма дълбочина ({$depth1} vs {$depth2}) при общо начало: '{$common_prefix}'";
                return $match1;
            } elseif ($depth2 > $depth1) {
                // Втората е по-дълбока
                $match2['match_info_details'] = "Избрана поради по-голяма дълбочина ({$depth2} vs {$depth1}) при общо начало: '{$common_prefix}'";
                return $match2;
            } else {
                // Еднаква дълбочина - проверяваме валидацията на крайните имена
                $validation1 = isset($match1['end_name_validation']) ? $match1['end_name_validation'] : null;
                $validation2 = isset($match2['end_name_validation']) ? $match2['end_name_validation'] : null;

                // Ако едното има валидна валидация, а другото не
                if ($validation1 && $validation1['valid'] && (!$validation2 || !$validation2['valid'])) {
                    $match1['match_info_details'] = "Избрана поради валидно крайно име при еднаква дълбочина ({$depth1})";
                    return $match1;
                } elseif ($validation2 && $validation2['valid'] && (!$validation1 || !$validation1['valid'])) {
                    $match2['match_info_details'] = "Избрана поради валидно крайно име при еднаква дълбочина ({$depth2})";
                    return $match2;
                }

                // И двете имат валидна или невалидна валидация - използваме score
                return $match1['score'] >= $match2['score'] ? $match1 : $match2;
            }
        } else {
            // Няма общо начало - използваме оригиналния приоритет (по score)
            return $match1['score'] >= $match2['score'] ? $match1 : $match2;
        }
    }

    /**
     * Проверява за ръчно дефинирани съответствия от конектора
     * @param string $source_path Път от синхронизиращия файл
     * @param array $site_categories Масив с OpenCart категории ['id' => 'път']
     * @param string $connector_key Ключ на конектора
     * @return array|null Ръчно дефинирано съвпадение или null
     */
    private function findManualCategoryMatch($source_path, $site_categories, $connector_key) {
        // Зареждаме конектора ако не е зареден
        $model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_key;
        if (!isset($this->$model_name)) {
            $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_key);
        }

        // Проверяваме дали конекторът има метод за ръчни съответствия
        if (!is_callable(array($this->$model_name, 'getCategoryMappings'))) {
            return null;
        }

        $manual_mappings = $this->$model_name->getCategoryMappings();
        if (empty($manual_mappings)) {
            return null;
        }

        $normalized_source = $this->normalizeTextForComparison($source_path);

        // Първо проверяваме за точни съвпадения (без %)
        foreach ($manual_mappings as $pattern => $target_path) {
            $normalized_pattern = $this->normalizeTextForComparison($pattern);

            // Ако няма % символи, проверяваме за точно съвпадение
            if (strpos($normalized_pattern, '%') === false) {
                if ($normalized_source === $normalized_pattern) {
                    // Намираме ID на категорията по пътя
                    $category_id = $this->findCategoryIdByPath($target_path, $site_categories);
                    if ($category_id) {
                        return [
                            'category_id' => $category_id,
                            'path' => $target_path,
                            'score' => 100,
                            'match_type' => 'manual_exact'
                        ];
                    }
                }
            }
        }

        // След това проверяваме за частични съвпадения (с %)
        foreach ($manual_mappings as $pattern => $target_path) {
            $normalized_pattern = $this->normalizeTextForComparison($pattern);

            // Ако има % символи, проверяваме за частично съвпадение
            if (strpos($normalized_pattern, '%') !== false) {
                if ($this->matchesWildcardPattern($normalized_source, $normalized_pattern)) {
                    // Намираме ID на категорията по пътя
                    $category_id = $this->findCategoryIdByPath($target_path, $site_categories);
                    if ($category_id) {
                        return [
                            'category_id' => $category_id,
                            'path' => $target_path,
                            'score' => 95, // Малко по-нисък от точното съвпадение
                            'match_type' => 'manual_wildcard'
                        ];
                    }
                }
            }
        }

        return null;
    }

    /**
     * Проверява дали текст съвпада с wildcard pattern (с % символи)
     * @param string $text Текст за проверка
     * @param string $pattern Pattern с % символи
     * @return bool
     */
    private function matchesWildcardPattern($text, $pattern) {
        // Ако няма % символи, проверяваме за точно съвпадение
        if (strpos($pattern, '%') === false) {
            return $text === $pattern;
        }

        // Разделяме pattern-а по % символите
        $parts = explode('%', $pattern);

        // Премахваме празните части
        $parts = array_filter($parts, function($part) {
            return trim($part) !== '';
        });

        // Ако няма части за проверка, връщаме true (само % символи)
        if (empty($parts)) {
            return true;
        }

        $text_position = 0;

        foreach ($parts as $index => $part) {
            $part = trim($part);
            if (empty($part)) {
                continue;
            }

            // Търсим частта в текста започвайки от текущата позиция
            $found_position = mb_stripos($text, $part, $text_position, 'UTF-8');

            if ($found_position === false) {
                // Частта не е намерена
                return false;
            }

            // Ако това е първата част и pattern-ът не започва с %,
            // то частта трябва да е в началото на текста
            if ($index === 0 && !$this->patternStartsWithWildcard($pattern)) {
                if ($found_position !== 0) {
                    return false;
                }
            }

            // Ако това е последната част и pattern-ът не завършва с %,
            // то частта трябва да е в края на текста
            if ($index === count($parts) - 1 && !$this->patternEndsWithWildcard($pattern)) {
                $expected_end_position = mb_strlen($text, 'UTF-8') - mb_strlen($part, 'UTF-8');
                if ($found_position !== $expected_end_position) {
                    return false;
                }
            }

            // Актуализираме позицията за следващото търсене
            $text_position = $found_position + mb_strlen($part, 'UTF-8');
        }

        return true;
    }

    /**
     * Проверява дали pattern започва с wildcard (%)
     * @param string $pattern Pattern за проверка
     * @return bool
     */
    private function patternStartsWithWildcard($pattern) {
        return mb_substr(ltrim($pattern), 0, 1, 'UTF-8') === '%';
    }

    /**
     * Проверява дали pattern завършва с wildcard (%)
     * @param string $pattern Pattern за проверка
     * @return bool
     */
    private function patternEndsWithWildcard($pattern) {
        return mb_substr(rtrim($pattern), -1, 1, 'UTF-8') === '%';
    }

    /**
     * Валидира дали крайните имена на две категории съвпадат
     * @param string $source_path Път на source категория
     * @param string $target_path Път на target категория
     * @return array Резултат от валидацията
     */
    private function validateCategoryEndNames($source_path, $target_path) {
        $source_end_name = $this->getLastCategoryName($source_path);
        $target_end_name = $this->getLastCategoryName($target_path);

        $normalized_source = $this->normalizeTextForComparison($source_end_name);
        $normalized_target = $this->normalizeTextForComparison($target_end_name);

        // Проверка за пълно съвпадение
        if ($normalized_source === $normalized_target) {
            return [
                'valid' => true,
                'match_type' => 'exact_end_name',
                'source_end' => $source_end_name,
                'target_end' => $target_end_name,
                'score_bonus' => 10
            ];
        }

        // Проверка за частично съвпадение (общи думи)
        $partial_match = $this->checkPartialEndNameMatch($normalized_source, $normalized_target);
        if ($partial_match['valid']) {
            return [
                'valid' => true,
                'match_type' => 'partial_end_name',
                'source_end' => $source_end_name,
                'target_end' => $target_end_name,
                'common_words' => $partial_match['common_words'],
                'score_bonus' => $partial_match['score_bonus']
            ];
        }

        // Няма съвпадение в крайните имена
        return [
            'valid' => false,
            'match_type' => 'no_end_name_match',
            'source_end' => $source_end_name,
            'target_end' => $target_end_name,
            'score_penalty' => -20
        ];
    }

    /**
     * Проверява за частично съвпадение между крайни имена на категории
     * @param string $normalized_source Нормализирано име на source категория
     * @param string $normalized_target Нормализирано име на target категория
     * @return array Резултат от проверката
     */
    private function checkPartialEndNameMatch($normalized_source, $normalized_target) {
        // Разделяме на думи
        $source_words = preg_split('/\s+/', $normalized_source);
        $target_words = preg_split('/\s+/', $normalized_target);

        // Премахваме кратки думи (под 3 символа) и общи думи
        $filter_words = ['за', 'на', 'от', 'до', 'със', 'без', 'под', 'над', 'при'];
        $source_words = array_filter($source_words, function($word) use ($filter_words) {
            return mb_strlen($word, 'UTF-8') >= 3 && !in_array($word, $filter_words);
        });
        $target_words = array_filter($target_words, function($word) use ($filter_words) {
            return mb_strlen($word, 'UTF-8') >= 3 && !in_array($word, $filter_words);
        });

        // Намираме общи думи
        $common_words = array_intersect($source_words, $target_words);

        if (count($common_words) > 0) {
            // Изчисляваме score bonus базиран на броя общи думи
            $total_words = count($source_words) + count($target_words);
            $common_ratio = (count($common_words) * 2) / $total_words;
            $score_bonus = round($common_ratio * 5); // Максимум 5 точки

            return [
                'valid' => true,
                'common_words' => array_values($common_words),
                'score_bonus' => $score_bonus
            ];
        }

        return ['valid' => false];
    }

    /**
     * Получава родителската категория (с една по-малка дълбочина)
     * @param string $category_path Път на категория
     * @return string|null Път на родителската категория или null
     */
    private function getParentCategoryPath($category_path) {
        $parts = explode(' > ', trim($category_path));
        if (count($parts) <= 1) {
            return null; // Няма родител
        }

        // Премахваме последната част
        array_pop($parts);
        return implode(' > ', $parts);
    }

    /**
     * Намира ID на категория по нейния път
     * @param string $target_path Път на категорията
     * @param array $site_categories Масив с OpenCart категории ['id' => 'път']
     * @return int|null ID на категорията или null
     */
    private function findCategoryIdByPath($target_path, $site_categories) {
        $normalized_target = $this->normalizeTextForComparison($target_path);

        foreach ($site_categories as $category_id => $category_path) {
            $normalized_category = $this->normalizeTextForComparison($category_path);
            if ($normalized_target === $normalized_category) {
                return $category_id;
            }
        }

        return null;
    }

    /**
     * Намира най-доброто частично съвпадение между път от файла и OpenCart категории
     * @param string $source_path Път от синхронизиращия файл
     * @param array $site_categories Масив с OpenCart категории ['id' => 'път']
     * @return array|null Най-доброто съвпадение или null
     */
    private function findBestCategoryMatch($source_path, $site_categories) {
        $normalized_source = $this->normalizeTextForComparison($source_path);
        $source_parts = explode(' > ', $normalized_source);

        $best_match = null;
        $best_score = 0;

        foreach ($site_categories as $category_id => $category_path) {
            $normalized_target = $this->normalizeTextForComparison($category_path);
            $target_parts = explode(' > ', $normalized_target);

            // Проверяваме за точно съвпадение
            if ($normalized_source === $normalized_target) {
                return [
                    'category_id' => $category_id,
                    'path' => $category_path,
                    'score' => 100,
                    'match_type' => 'exact'
                ];
            }

            // Проверяваме за частично съвпадение
            $score = $this->calculatePartialMatchScore($source_parts, $target_parts);

            if ($score > $best_score && $score >= 50) { // Минимален праг от 50%
                $candidate_match = [
                    'category_id' => $category_id,
                    'path' => $category_path,
                    'score' => $score,
                    'match_type' => 'partial'
                ];

                // Прилагаме валидация на крайните имена
                $validation = $this->validateCategoryEndNames($source_path, $category_path);
                $candidate_match['end_name_validation'] = $validation;

                // Актуализираме score-а според валидацията
                if ($validation['valid']) {
                    $candidate_match['score'] += $validation['score_bonus'];
                    $candidate_match['validation_status'] = 'passed';
                } else {
                    // При неуспешна валидация, търсим родителската категория
                    $parent_path = $this->getParentCategoryPath($category_path);
                    if ($parent_path) {
                        // Намираме ID на родителската категория
                        $parent_id = $this->findCategoryIdByPath($parent_path, $site_categories);
                        if ($parent_id) {
                            $candidate_match['path'] = $parent_path;
                            $candidate_match['category_id'] = $parent_id;
                            $candidate_match['score'] += $validation['score_penalty']; // Прилагаме penalty
                            $candidate_match['validation_status'] = 'fallback_to_parent';
                            $candidate_match['original_path'] = $category_path; // Запазваме оригиналния път
                        } else {
                            $candidate_match['score'] += $validation['score_penalty'];
                            $candidate_match['validation_status'] = 'failed';
                        }
                    } else {
                        $candidate_match['score'] += $validation['score_penalty'];
                        $candidate_match['validation_status'] = 'failed';
                    }
                }

                // Проверяваме дали новият кандидат е по-добър
                if ($candidate_match['score'] > $best_score) {
                    $best_score = $candidate_match['score'];
                    $best_match = $candidate_match;
                }
            }
        }

        return $best_match;
    }

    /**
     * Изчислява резултат за частично съвпадение между два пътя на категории
     * @param array $source_parts Части от пътя на източника
     * @param array $target_parts Части от пътя на целта
     * @return float Резултат от 0 до 100
     */
    private function calculatePartialMatchScore($source_parts, $target_parts) {
        $matches = 0;
        $total_parts = max(count($source_parts), count($target_parts));

        // Проверяваме за съвпадения в последователност
        $source_count = count($source_parts);
        $target_count = count($target_parts);

        // Проверяваме дали целевият път е подмножество на източника
        for ($i = 0; $i < $target_count; $i++) {
            for ($j = 0; $j < $source_count; $j++) {
                if ($target_parts[$i] === $source_parts[$j]) {
                    $matches++;
                    break;
                }
            }
        }

        // Бонус за съвпадение на последната част (най-специфичната категория)
        if ($source_count > 0 && $target_count > 0) {
            $last_source = $source_parts[$source_count - 1];
            $last_target = $target_parts[$target_count - 1];

            if ($last_source === $last_target) {
                $matches += 0.5; // Бонус за съвпадение на последната част
            }
        }

        return $total_parts > 0 ? ($matches / $total_parts) * 100 : 0;
    }

    /**
     * AJAX: Получава съответствията на категориите за конектор
     */
    public function getCategoriesMapping() {
        $json = [];
        $this->load->language('extension/module/multi_feed_syncer');

        if (isset($this->request->get['mfsc_id'])) {
            $mfsc_id = (int)$this->request->get['mfsc_id'];
            $this->load->model('extension/module/multi_feed_syncer');
            $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);

            if ($connector_info && file_exists(self::CONNECTOR_PATH . $connector_info['connector_key'] . '.php')) {
                $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_info['connector_key']);
                $model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_info['connector_key'];

                if (is_callable(array($this->$model_name, 'getCategoriesFromFile'))) {
                    try {
                        // Получаваме категориите от файла на конектора
                        $source_categories = $this->$model_name->getCategoriesFromFile();

                        // Получаваме запазените съответствия
                        $saved_mappings = $this->model_extension_module_multi_feed_syncer->getCategoriesMapping($mfsc_id);

                        // Получаваме всички OpenCart категории
                        $site_categories = $this->model_extension_module_multi_feed_syncer->getAllSiteCategories();

                        // Създаваме мапинг за по-лесен достъп към запазените съответствия
                        $mappings_map = [];
                        foreach ($saved_mappings as $mapping) {
                            $mappings_map[$mapping['source_category_path']] = $mapping['target_category_path'];
                        }

                        // Подготвяме резултата
                        $categories_data = [];

                        // Добавяме категориите от конектора с автоматично мапиране
                        foreach ($source_categories as $source_path) {
                            $target_path = '';
                            $is_new = true;
                            $auto_suggested = false;
                            $match_info = null;

                            if (isset($mappings_map[$source_path])) {
                                // НАЙ-ВИСОК ПРИОРИТЕТ: Има запазено съответствие от базата данни
                                // Използваме директно запазеното съответствие без допълнителни алгоритми
                                $target_path = $mappings_map[$source_path];
                                $is_new = false;
                                $auto_suggested = false; // Не е автоматично предложено, а ръчно запазено
                                // Не добавяме match_info за запазени съответствия
                            } else {
                                // Няма запазено съответствие - търсим съвпадение с приоритет

                                // 1. Получаваме ръчно дефинирано съответствие от конектора
                                $manual_match = $this->findManualCategoryMatch($source_path, $site_categories, $connector_info['connector_key']);

                                // 2. Получаваме автоматично съответствие
                                $auto_match = $this->findBestCategoryMatch($source_path, $site_categories);

                                // 3. Избираме най-доброто съвпадение според правилата за приоритет
                                $best_match = $this->chooseBestMatch($manual_match, $auto_match);

                                if ($best_match) {
                                    $target_path = $best_match['path'];
                                    $auto_suggested = true;
                                    $match_info = [
                                        'score' => $best_match['score'],
                                        'match_type' => $best_match['match_type'],
                                        'category_id' => $best_match['category_id']
                                    ];

                                    // Добавяме детайли за избора ако има такива
                                    if (isset($best_match['match_info_details'])) {
                                        $match_info['details'] = $best_match['match_info_details'];
                                    }

                                    // Добавяме информация за валидацията на крайното име ако има
                                    if (isset($best_match['end_name_validation'])) {
                                        $match_info['end_name_validation'] = $best_match['end_name_validation'];
                                    }

                                    // Добавяме статус на валидацията ако има
                                    if (isset($best_match['validation_status'])) {
                                        $match_info['validation_status'] = $best_match['validation_status'];
                                    }

                                    // Добавяме оригиналния път ако е fallback към родителска категория
                                    if (isset($best_match['original_path'])) {
                                        $match_info['original_path'] = $best_match['original_path'];
                                    }

                                    // Добавяме информация за сравнението ако има и двете съвпадения
                                    if ($manual_match && $auto_match) {
                                        $match_info['comparison'] = [
                                            'manual_path' => $manual_match['path'],
                                            'manual_depth' => $this->getCategoryDepth($manual_match['path']),
                                            'auto_path' => $auto_match['path'],
                                            'auto_depth' => $this->getCategoryDepth($auto_match['path']),
                                            'common_prefix' => $this->getCommonCategoryPrefix($manual_match['path'], $auto_match['path']),
                                            'chosen' => $best_match === $manual_match ? 'manual' : 'auto'
                                        ];
                                    }
                                }
                            }

                            $category_data = [
                                'source_path' => $source_path,
                                'target_path' => $target_path,
                                'is_new' => $is_new,
                                'auto_suggested' => $auto_suggested,
                                'type' => 'source',
                                'suggested_category_name' => $this->getLastCategoryName($source_path) // Предложено име за нова категория
                            ];

                            // Добавяме информация за съвпадението, ако има такова
                            if ($match_info) {
                                $category_data['match_info'] = $match_info;
                            }

                            $categories_data[] = $category_data;
                        }

                        // Премахваме логиката за добавяне на OpenCart категории като отделни редове
                        // Таблицата трябва да показва САМО source категориите от синхронизиращия файл
                        // OpenCart категориите се използват само за автозавършване в target_path полетата

                        // Изчисляваме статистики за автоматичното мапиране
                        $auto_mapping_stats = [
                            'total_source_categories' => count($source_categories),
                            'manually_mapped' => count($saved_mappings),
                            'auto_suggested' => 0,
                            'unmapped' => 0
                        ];

                        foreach ($categories_data as $cat_data) {
                            if ($cat_data['type'] === 'source') {
                                if (!empty($cat_data['auto_suggested'])) {
                                    $auto_mapping_stats['auto_suggested']++;
                                } elseif ($cat_data['is_new']) {
                                    $auto_mapping_stats['unmapped']++;
                                }
                            }
                        }

                        $json['success'] = true;
                        $json['categories'] = $categories_data;
                        $json['site_categories'] = $site_categories; // Добавяме всички сайт категории за автозавършване
                        $json['auto_mapping_stats'] = $auto_mapping_stats; // Статистики за автоматичното мапиране
                    } catch (Exception $e) {
                        $json['error'] = 'Грешка при извличане на категории: ' . $e->getMessage();
                    }
                } else {
                    $json['error'] = 'Методът getCategoriesFromFile не е намерен в конектора.';
                }
            } else {
                $json['error'] = 'Конекторът не е намерен.';
            }
        } else {
            $json['error'] = 'Липсва ID на конектор.';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: Запазва съответствията на категориите
     */
    public function saveCategoriesMapping() {
        $json = [];
        $this->load->language('extension/module/multi_feed_syncer');

        if (!$this->user->hasPermission('modify', 'extension/module/multi_feed_syncer')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->post['mfsc_id']) && isset($this->request->post['mappings'])) {
                $mfsc_id = (int)$this->request->post['mfsc_id'];
                $mappings = $this->request->post['mappings'];

                $this->load->model('extension/module/multi_feed_syncer');

                try {
                    $created_mappings_count = 0;
                    $updated_mappings_count = 0;
                    $unchanged_mappings_count = 0;
                    $created_categories_count = 0;
                    $created_categories_info = [];
                    $mapping_operations = [];

                    foreach ($mappings as $mapping) {
                        $target_path = '';

                        // Проверяваме дали трябва да създадем нова категория
                        if (!empty($mapping['create_new_category']) && $mapping['create_new_category'] === true) {
                            if (!empty($mapping['new_category_name'])) {
                                $parent_id = !empty($mapping['new_category_parent_id']) ? (int)$mapping['new_category_parent_id'] : 0;

                                // Създаваме новата категория
                                $new_category_id = $this->model_extension_module_multi_feed_syncer->createNewCategory(
                                    $mapping['new_category_name'],
                                    $parent_id
                                );

                                // Генерираме пълния път на новосъздадената категория
                                $target_path = $this->model_extension_module_multi_feed_syncer->getCategoryPath($new_category_id);

                                $created_categories_count++;
                                $created_categories_info[] = [
                                    'name' => $mapping['new_category_name'],
                                    'path' => $target_path,
                                    'id' => $new_category_id
                                ];
                            }
                        } else {
                            // Използваме съществуващия target_path
                            $target_path = !empty($mapping['target_path']) ? $mapping['target_path'] : '';
                        }

                        // Запазваме mapping-а ако имаме и source_path и target_path
                        if (!empty($mapping['source_path']) && !empty($target_path)) {
                            $operation_result = $this->model_extension_module_multi_feed_syncer->saveOrUpdateCategoryMapping(
                                $mfsc_id,
                                $mapping['source_path'],
                                $target_path
                            );

                            // Броим операциите по тип
                            switch ($operation_result['action']) {
                                case 'created':
                                    $created_mappings_count++;
                                    break;
                                case 'updated':
                                    $updated_mappings_count++;
                                    break;
                                case 'unchanged':
                                    $unchanged_mappings_count++;
                                    break;
                            }

                            // Запазваме информация за операцията
                            $mapping_operations[] = [
                                'source_path' => $mapping['source_path'],
                                'action' => $operation_result['action'],
                                'mapping_id' => $operation_result['mapping_id'],
                                'target_path' => $target_path
                            ];
                        }
                    }

                    // Формираме детайлното съобщение за успех
                    $success_parts = [];

                    if ($created_mappings_count > 0) {
                        $success_parts[] = "Създадени са {$created_mappings_count} нови съответствия";
                    }

                    if ($updated_mappings_count > 0) {
                        $success_parts[] = "Актуализирани са {$updated_mappings_count} съществуващи съответствия";
                    }

                    if ($unchanged_mappings_count > 0) {
                        $success_parts[] = "{$unchanged_mappings_count} съответствия останаха непроменени";
                    }

                    if ($created_categories_count > 0) {
                        $success_parts[] = "Създадени са {$created_categories_count} нови категории";
                    }

                    $success_message = !empty($success_parts) ? implode('. ', $success_parts) . '.' : 'Няма промени за запазване.';

                    $json['success'] = $success_message;
                    $json['created_categories'] = $created_categories_info;
                    $json['mapping_statistics'] = [
                        'created' => $created_mappings_count,
                        'updated' => $updated_mappings_count,
                        'unchanged' => $unchanged_mappings_count,
                        'total_processed' => count($mapping_operations)
                    ];
                    $json['mapping_operations'] = $mapping_operations;
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
                }
            } else {
                $json['error'] = 'Липсват необходимите данни.';
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: Търси категории от сайта за автозавършване
     */
    public function searchCategories() {
        $json = [];
        $this->load->language('extension/module/multi_feed_syncer');

        if (isset($this->request->get['search'])) {
            $search_term = trim($this->request->get['search']);
            $limit = isset($this->request->get['limit']) ? (int)$this->request->get['limit'] : 10;

            if (!empty($search_term)) {
                $this->load->model('extension/module/multi_feed_syncer');

                try {
                    $categories = $this->model_extension_module_multi_feed_syncer->searchSiteCategories($search_term, $limit);

                    $suggestions = [];
                    foreach ($categories as $category) {
                        $path = $this->model_extension_module_multi_feed_syncer->getCategoryPath($category['category_id']);
                        $suggestions[] = [
                            'id' => $category['category_id'],
                            'name' => $category['name'],
                            'path' => $path
                        ];
                    }

                    $json['success'] = true;
                    $json['suggestions'] = $suggestions;
                } catch (Exception $e) {
                    $json['error'] = 'Грешка при търсене: ' . $e->getMessage();
                }
            } else {
                $json['suggestions'] = [];
            }
        } else {
            $json['error'] = 'Липсва термин за търсене.';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: Получава предложено име за нова категория от source path
     */
    public function getSuggestedCategoryName() {
        $json = [];

        if (isset($this->request->get['source_path'])) {
            $source_path = trim($this->request->get['source_path']);

            if (!empty($source_path)) {
                $suggested_name = $this->getLastCategoryName($source_path);
                $json['success'] = true;
                $json['suggested_name'] = $suggested_name;
            } else {
                $json['error'] = 'Празен source path.';
            }
        } else {
            $json['error'] = 'Липсва source path.';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: Стартира процеса на обновяване на продуктите
     */
    public function startProductsUpdate() {
        $json = [];
        $this->load->language('extension/module/multi_feed_syncer');

        if (!$this->user->hasPermission('modify', 'extension/module/multi_feed_syncer')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->post['mfsc_id'])) {
                $mfsc_id = (int)$this->request->post['mfsc_id'];

                $this->load->model('extension/module/multi_feed_syncer');
                $connector_info = $this->model_extension_module_multi_feed_syncer->getConnector($mfsc_id);

                if ($connector_info && file_exists(self::CONNECTOR_PATH . $connector_info['connector_key'] . '.php')) {
                    $this->load->model('extension/module/multi_feed_syncer_connectors/' . $connector_info['connector_key']);
                    $model_name = 'model_extension_module_multi_feed_syncer_connectors_' . $connector_info['connector_key'];

                    if (is_callable([$this->$model_name, 'processSupplierFeed'])) {
                        try {
                            $start_time = microtime(true);
                            $sync_stats = $this->$model_name->processSupplierFeed(
                                $connector_info['connector_key'],
                                $mfsc_id,
                                false, // не е тестов режим
                                $this->model_extension_module_multi_feed_syncer
                            );
                            $end_time = microtime(true);
                            $execution_time = round($end_time - $start_time, 2);

                            // Записване на лог за синхронизацията
                            $this->model_extension_module_multi_feed_syncer->addSynchronizationLog(
                                $mfsc_id,
                                $sync_stats,
                                $execution_time
                            );

                            $json['success'] = 'Обновяването на продуктите завърши успешно.';
                            $json['stats'] = [
                                'added' => isset($sync_stats['added']) ? $sync_stats['added'] : 0,
                                'updated' => isset($sync_stats['updated']) ? $sync_stats['updated'] : 0,
                                'skipped' => isset($sync_stats['skipped']) ? $sync_stats['skipped'] : 0,
                                'errors' => isset($sync_stats['errors']) ? $sync_stats['errors'] : 0,
                                'execution_time' => $execution_time
                            ];

                            if (function_exists('LogDeveloper')) {
                                LogDeveloper('Стартиране на обновяване на продукти за конектор ' . $connector_info['connector'] . ' (ID: ' . $mfsc_id . ') от потребител: ' . $this->user->getUserName());
                            }
                        } catch (Exception $e) {
                            $json['error'] = 'Грешка при обновяване на продуктите: ' . $e->getMessage();
                        }
                    } else {
                        $json['error'] = 'Методът processSupplierFeed не е намерен.';
                    }
                } else {
                    $json['error'] = 'Конекторът не е намерен.';
                }
            } else {
                $json['error'] = 'Липсва ID на конектор.';
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
}
?>